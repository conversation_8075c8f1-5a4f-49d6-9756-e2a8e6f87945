// Simple test to check imports
console.log('Starting import test...');

try {
    console.log('Testing content analyzer...');
    const contentAnalyzer = await import('./netlify/functions/content-analyzer.js');
    console.log('✅ Content analyzer imported');
    
    console.log('Testing text modifiers...');
    const textModifiers = await import('./netlify/functions/text-modifiers-advanced.js');
    console.log('✅ Text modifiers imported');
    
    console.log('Testing NLTK humanizer...');
    const nltkHumanizer = await import('./netlify/functions/nltk-humanizer.js');
    console.log('✅ NLTK humanizer imported');
    
    console.log('Testing advanced humanizer...');
    const advancedHumanizer = await import('./netlify/functions/advanced-humanizer.js');
    console.log('✅ Advanced humanizer imported');
    
    console.log('Testing balanced humanizer...');
    const balancedHumanizer = await import('./netlify/functions/balanced-humanizer.js');
    console.log('✅ Balanced humanizer imported');
    
    console.log('Testing commercial humanizer...');
    const commercialHumanizer = await import('./netlify/functions/commercial-humanizer.js');
    console.log('✅ Commercial humanizer imported');
    
    console.log('Testing main process...');
    const process = await import('./netlify/functions/process.js');
    console.log('✅ Main process imported');
    
    console.log('🎉 All imports successful!');
    
} catch (error) {
    console.error('❌ Import failed:', error.message);
    console.error(error.stack);
}
