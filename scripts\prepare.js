#!/usr/bin/env node

/**
 * Prepare script that safely handles husky installation
 * Only runs husky install if husky is available (in development)
 */

import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Get __dirname equivalent in ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

try {
  // Check if we're in a CI/production environment
  const isCI = process.env.CI || process.env.NETLIFY || process.env.VERCEL;
  const isProduction = process.env.NODE_ENV === 'production';
  
  if (isCI || isProduction) {
    console.log('Skipping husky installation in CI/production environment');
    process.exit(0);
  }

  // Check if husky is available
  const huskyPath = path.join(process.cwd(), 'node_modules', 'husky');
  if (!fs.existsSync(huskyPath)) {
    console.log('<PERSON>sky not found, skipping installation');
    process.exit(0);
  }

  // Try to run husky install
  console.log('Installing husky...');
  execSync('husky install', { stdio: 'inherit' });
  console.log('<PERSON><PERSON> installed successfully');
  
} catch (error) {
  console.warn('Warning: Could not install husky:', error.message);
  // Don't fail the build if husky installation fails
  process.exit(0);
}
