/**
 * Advanced Humanization Engine for Netlify
 * Complete port of localhost advancedHumanizer.js functionality
 * Designed to achieve ≤10% AI detection with pattern-based techniques
 */

// AI Pattern Detection and Analysis
const AI_PATTERNS = {
    // Formal/robotic language patterns
    FORMAL_PATTERNS: [
        /\b(furthermore|moreover|additionally|consequently|therefore|thus|hence)\b/gi,
        /\b(utilize|implement|facilitate|optimize|enhance|leverage)\b/gi,
        /\b(comprehensive|extensive|significant|substantial|considerable)\b/gi,
        /\b(in order to|with regard to|in terms of|as a result of)\b/gi
    ],
    
    // Repetitive sentence structures
    REPETITIVE_STRUCTURES: [
        /^(The|This|That|These|Those)\s+\w+\s+(is|are|was|were)/gm,
        /\b(It is important to|It should be noted that|It is worth mentioning)\b/gi,
        /\b(In conclusion|To summarize|In summary|Overall)\b/gi
    ],
    
    // Passive voice overuse
    PASSIVE_VOICE: [
        /\b(is|are|was|were|being|been)\s+\w+ed\b/gi,
        /\b(can be|will be|should be|must be)\s+\w+ed\b/gi
    ],
    
    // AI-specific vocabulary
    AI_VOCABULARY: [
        /\b(delve|delving|delves)\b/gi,
        /\b(realm|landscape|paradigm|framework)\b/gi,
        /\b(cutting-edge|state-of-the-art|innovative|revolutionary)\b/gi,
        /\b(seamlessly|effortlessly|efficiently|effectively)\b/gi
    ]
};

/**
 * Comprehensive AI pattern analysis
 */
function comprehensiveAIPatternAnalysis(text) {
    let totalPatterns = 0;
    const detectedPatterns = {};
    
    Object.entries(AI_PATTERNS).forEach(([category, patterns]) => {
        detectedPatterns[category] = 0;
        patterns.forEach(pattern => {
            const matches = (text.match(pattern) || []).length;
            detectedPatterns[category] += matches;
            totalPatterns += matches;
        });
    });
    
    const textLength = text.length;
    const severity = totalPatterns / (textLength / 1000); // patterns per 1000 chars
    
    return {
        totalPatterns,
        detectedPatterns,
        severity,
        hasHighAIDetection: severity > 5,
        recommendations: generateAIPatternRecommendations(detectedPatterns, severity)
    };
}

/**
 * Generate recommendations based on AI pattern analysis
 */
function generateAIPatternRecommendations(patterns, severity) {
    const recommendations = [];
    
    if (patterns.FORMAL_PATTERNS > 3) {
        recommendations.push('Replace formal language with casual alternatives');
    }
    if (patterns.REPETITIVE_STRUCTURES > 2) {
        recommendations.push('Vary sentence structures and openings');
    }
    if (patterns.PASSIVE_VOICE > 5) {
        recommendations.push('Convert passive voice to active voice');
    }
    if (patterns.AI_VOCABULARY > 2) {
        recommendations.push('Replace AI-specific vocabulary with natural alternatives');
    }
    
    return recommendations;
}

/**
 * Dynamic aggressiveness calculation based on AI patterns
 */
function dynamicAggressivenessCalculation(text, baseAggressiveness, aiAnalysis, targetDetection) {
    let adjustedAggressiveness = baseAggressiveness;
    
    // Increase aggressiveness for high AI detection
    if (aiAnalysis.hasHighAIDetection) {
        adjustedAggressiveness = Math.min(1.0, baseAggressiveness + 0.2);
    }
    
    // Adjust based on target detection level
    if (targetDetection <= 5) {
        adjustedAggressiveness = Math.min(1.0, adjustedAggressiveness + 0.3);
    } else if (targetDetection <= 10) {
        adjustedAggressiveness = Math.min(1.0, adjustedAggressiveness + 0.1);
    }
    
    // Adjust based on pattern severity
    if (aiAnalysis.severity > 10) {
        adjustedAggressiveness = Math.min(1.0, adjustedAggressiveness + 0.2);
    }
    
    return adjustedAggressiveness;
}

/**
 * Analyze text structure for informed processing
 */
function analyzeTextStructure(text) {
    const sentences = text.split(/(?<=[.!?])\s+/);
    const words = text.split(/\s+/);
    
    return {
        sentenceCount: sentences.length,
        averageSentenceLength: sentences.reduce((sum, s) => sum + s.length, 0) / sentences.length,
        wordCount: words.length,
        averageWordLength: words.reduce((sum, w) => sum + w.length, 0) / words.length,
        sentenceLengths: sentences.map(s => s.length),
        complexSentenceRatio: sentences.filter(s => s.includes(',') || s.includes(';')).length / sentences.length,
        formalWordCount: countFormalWords(text),
        aiTriggerCount: countAITriggers(text)
    };
}

/**
 * Count formal words in text
 */
function countFormalWords(text) {
    const formalWords = [
        'furthermore', 'moreover', 'additionally', 'consequently', 'therefore',
        'utilize', 'implement', 'facilitate', 'optimize', 'enhance', 'leverage',
        'comprehensive', 'extensive', 'significant', 'substantial', 'considerable'
    ];
    
    let count = 0;
    formalWords.forEach(word => {
        const regex = new RegExp(`\\b${word}\\b`, 'gi');
        count += (text.match(regex) || []).length;
    });
    
    return count;
}

/**
 * Count AI trigger words
 */
function countAITriggers(text) {
    const aiTriggers = [
        'delve', 'realm', 'landscape', 'paradigm', 'framework',
        'cutting-edge', 'state-of-the-art', 'innovative', 'revolutionary',
        'seamlessly', 'effortlessly', 'efficiently', 'effectively'
    ];
    
    let count = 0;
    aiTriggers.forEach(word => {
        const regex = new RegExp(`\\b${word}\\b`, 'gi');
        count += (text.match(regex) || []).length;
    });
    
    return count;
}

/**
 * Enhanced sentence restructuring with multiple techniques
 */
function enhancedSentenceRestructuring(text, textAnalysis, aggressiveness, protectedSentences = [], contentTypeAnalysis = {}) {
    let result = text;

    // Split into sentences for processing
    const sentences = result.split(/(?<=[.!?])\s+/);
    const restructuredSentences = [];

    sentences.forEach((sentence, index) => {
        if (!sentence.trim()) {
            restructuredSentences.push(sentence);
            return;
        }

        // Skip protected sentences
        if (protectedSentences.includes(sentence.trim())) {
            restructuredSentences.push(sentence);
            return;
        }

        let processedSentence = sentence;

        // Apply various restructuring techniques based on aggressiveness
        if (Math.random() < aggressiveness * 0.3) {
            processedSentence = convertPassiveToActive(processedSentence);
        }

        if (Math.random() < aggressiveness * 0.2) {
            processedSentence = varyStartingWords(processedSentence);
        }

        if (Math.random() < aggressiveness * 0.25) {
            processedSentence = restructureSentenceOrder(processedSentence);
        }

        if (Math.random() < aggressiveness * 0.15) {
            processedSentence = addTransitionalPhrases(processedSentence, index, sentences.length);
        }

        restructuredSentences.push(processedSentence);
    });

    return restructuredSentences.join(' ');
}

/**
 * Convert passive voice to active voice
 */
function convertPassiveToActive(sentence) {
    // Simple passive to active conversions
    const passivePatterns = [
        {
            pattern: /(\w+)\s+(is|are|was|were)\s+(\w+ed)\s+by\s+(\w+)/gi,
            replacement: '$4 $3 $1'
        },
        {
            pattern: /(\w+)\s+(is|are|was|were)\s+(\w+ed)/gi,
            replacement: (match, subject, verb, pastParticiple) => {
                // Simple conversion - this could be more sophisticated
                return `${subject} ${pastParticiple.replace(/ed$/, 's')}`;
            }
        }
    ];

    let result = sentence;
    passivePatterns.forEach(({ pattern, replacement }) => {
        result = result.replace(pattern, replacement);
    });

    return result;
}

/**
 * Vary sentence starting words to reduce repetition
 */
function varyStartingWords(sentence) {
    const commonStarters = {
        'The ': ['This ', 'That ', 'A ', 'An '],
        'This ': ['The ', 'That ', 'Such '],
        'That ': ['This ', 'The ', 'Such '],
        'It is ': ['This is ', 'That is ', 'What is '],
        'There are ': ['We find ', 'You can see ', 'Many '],
        'There is ': ['We find ', 'You can see ', 'One ']
    };

    for (const [starter, alternatives] of Object.entries(commonStarters)) {
        if (sentence.startsWith(starter)) {
            const randomAlt = alternatives[Math.floor(Math.random() * alternatives.length)];
            return randomAlt + sentence.substring(starter.length);
        }
    }

    return sentence;
}

/**
 * Restructure sentence order for variety
 */
function restructureSentenceOrder(sentence) {
    // Simple clause reordering for sentences with commas
    if (sentence.includes(',') && sentence.split(',').length === 2) {
        const parts = sentence.split(',');
        if (parts[0].trim().length > 10 && parts[1].trim().length > 10) {
            // Randomly swap clauses
            if (Math.random() < 0.5) {
                return parts[1].trim() + ', ' + parts[0].trim().toLowerCase();
            }
        }
    }

    return sentence;
}

/**
 * Add transitional phrases for flow
 */
function addTransitionalPhrases(sentence, index, totalSentences) {
    const transitions = {
        beginning: ['Actually, ', 'Well, ', 'So, ', 'Now, '],
        middle: ['However, ', 'Meanwhile, ', 'Additionally, ', 'Also, '],
        end: ['Finally, ', 'Ultimately, ', 'In the end, ', 'Overall, ']
    };

    let position = 'middle';
    if (index === 0) position = 'beginning';
    if (index === totalSentences - 1) position = 'end';

    const availableTransitions = transitions[position];
    const randomTransition = availableTransitions[Math.floor(Math.random() * availableTransitions.length)];

    return randomTransition + sentence.charAt(0).toLowerCase() + sentence.slice(1);
}

/**
 * Advanced context-aware synonym replacement
 */
function advancedContextAwareSynonymReplacement(text, aggressiveness, protectedSentences = [], contentTypeAnalysis = {}) {
    // Advanced synonym database for context-aware replacement
    const contextualSynonyms = {
        // Formal to casual replacements
        'utilize': ['use', 'employ', 'apply'],
        'implement': ['put in place', 'set up', 'carry out'],
        'facilitate': ['help', 'make easier', 'enable'],
        'optimize': ['improve', 'enhance', 'fine-tune'],
        'leverage': ['use', 'take advantage of', 'harness'],
        'comprehensive': ['complete', 'thorough', 'full'],
        'extensive': ['wide', 'broad', 'large'],
        'significant': ['important', 'major', 'big'],
        'substantial': ['large', 'considerable', 'major'],
        'furthermore': ['also', 'plus', 'what\'s more'],
        'moreover': ['also', 'besides', 'on top of that'],
        'additionally': ['also', 'plus', 'as well'],
        'consequently': ['so', 'therefore', 'as a result'],
        'therefore': ['so', 'thus', 'for this reason'],

        // AI-specific vocabulary replacements
        'delve': ['explore', 'look into', 'examine'],
        'realm': ['area', 'field', 'world'],
        'landscape': ['scene', 'environment', 'situation'],
        'paradigm': ['model', 'approach', 'way'],
        'framework': ['structure', 'system', 'setup'],
        'cutting-edge': ['latest', 'modern', 'new'],
        'state-of-the-art': ['latest', 'modern', 'advanced'],
        'innovative': ['new', 'creative', 'original'],
        'revolutionary': ['groundbreaking', 'game-changing', 'major'],
        'seamlessly': ['smoothly', 'easily', 'without issues'],
        'effortlessly': ['easily', 'simply', 'without trouble'],
        'efficiently': ['effectively', 'well', 'smartly'],
        'effectively': ['well', 'successfully', 'properly']
    };

    let result = text;

    // Apply synonym replacement with context awareness
    Object.entries(contextualSynonyms).forEach(([original, synonyms]) => {
        const regex = new RegExp(`\\b${original}\\b`, 'gi');
        result = result.replace(regex, (match) => {
            // Apply replacement based on aggressiveness
            if (Math.random() < aggressiveness * 0.4) {
                const synonym = synonyms[Math.floor(Math.random() * synonyms.length)];
                // Preserve original case
                if (match === match.toUpperCase()) {
                    return synonym.toUpperCase();
                } else if (match[0] === match[0].toUpperCase()) {
                    return synonym.charAt(0).toUpperCase() + synonym.slice(1);
                } else {
                    return synonym;
                }
            }
            return match;
        });
    });

    return result;
}

/**
 * Enhance perplexity and burstiness for human-like variation
 */
function enhancePerplexityAndBurstiness(text, textAnalysis, aggressiveness, protectedSentences = []) {
    let result = text;

    // Add sentence length variation (burstiness)
    const sentences = result.split(/(?<=[.!?])\s+/);
    const processedSentences = [];

    sentences.forEach((sentence, index) => {
        if (!sentence.trim() || protectedSentences.includes(sentence.trim())) {
            processedSentences.push(sentence);
            return;
        }

        let processedSentence = sentence;

        // Randomly combine short sentences or split long ones
        if (Math.random() < aggressiveness * 0.2) {
            if (sentence.length < 50 && index < sentences.length - 1) {
                // Combine with next sentence occasionally
                const nextSentence = sentences[index + 1];
                if (nextSentence && nextSentence.length < 50) {
                    processedSentence = sentence + ' ' + nextSentence;
                    sentences[index + 1] = ''; // Mark as processed
                }
            } else if (sentence.length > 150) {
                // Split long sentences at natural break points
                const breakPoints = [', and ', ', but ', ', or ', ', which ', ', that '];
                for (const breakPoint of breakPoints) {
                    if (sentence.includes(breakPoint)) {
                        const parts = sentence.split(breakPoint);
                        if (parts.length === 2 && parts[0].length > 30 && parts[1].length > 30) {
                            processedSentence = parts[0] + '. ' + parts[1].charAt(0).toUpperCase() + parts[1].slice(1);
                            break;
                        }
                    }
                }
            }
        }

        processedSentences.push(processedSentence);
    });

    return processedSentences.filter(s => s.trim()).join(' ');
}

/**
 * Sophisticated human writing pattern injection
 */
function sophisticatedHumanPatternInjection(text, aggressiveness, hesitationFrequency, protectedSentences = [], contentTypeAnalysis = {}) {
    let result = text;

    // Add hesitation patterns (controlled frequency)
    result = addSophisticatedHesitationPatterns(result, aggressiveness, hesitationFrequency, protectedSentences);

    // Add conversational elements
    result = addAuthenticConversationalElements(result, aggressiveness * 0.6, protectedSentences);

    // Add natural human inconsistencies
    result = addNaturalHumanInconsistencies(result, aggressiveness * 0.4);

    return result;
}

/**
 * Add sophisticated hesitation patterns
 */
function addSophisticatedHesitationPatterns(text, aggressiveness, hesitationFrequency, protectedSentences = []) {
    const hesitationMarkers = [
        'I think', 'I believe', 'I guess', 'probably', 'maybe', 'perhaps',
        'sort of', 'kind of', 'basically', 'actually', 'well'
    ];

    const sentences = text.split(/(?<=[.!?])\s+/);
    const processedSentences = [];

    sentences.forEach(sentence => {
        if (!sentence.trim() || protectedSentences.includes(sentence.trim())) {
            processedSentences.push(sentence);
            return;
        }

        let processedSentence = sentence;

        // Add hesitation markers with controlled frequency
        if (Math.random() < hesitationFrequency * aggressiveness * 0.1) {
            const marker = hesitationMarkers[Math.floor(Math.random() * hesitationMarkers.length)];

            // Insert at beginning or middle of sentence
            if (Math.random() < 0.5) {
                processedSentence = marker + ', ' + sentence.charAt(0).toLowerCase() + sentence.slice(1);
            } else {
                // Insert in middle at natural break points
                const breakPoints = [', ', ' and ', ' but ', ' or '];
                for (const breakPoint of breakPoints) {
                    if (sentence.includes(breakPoint)) {
                        processedSentence = sentence.replace(breakPoint, breakPoint + marker + ', ');
                        break;
                    }
                }
            }
        }

        processedSentences.push(processedSentence);
    });

    return processedSentences.join(' ');
}

/**
 * Add authentic conversational elements
 */
function addAuthenticConversationalElements(text, aggressiveness, protectedSentences = []) {
    const conversationalElements = [
        'you know', 'I mean', 'like', 'right?', 'okay?', 'see what I mean?',
        'anyway', 'so yeah', 'pretty much', 'basically'
    ];

    const sentences = text.split(/(?<=[.!?])\s+/);
    const processedSentences = [];

    sentences.forEach(sentence => {
        if (!sentence.trim() || protectedSentences.includes(sentence.trim())) {
            processedSentences.push(sentence);
            return;
        }

        let processedSentence = sentence;

        // Add conversational elements sparingly
        if (Math.random() < aggressiveness * 0.1) {
            const element = conversationalElements[Math.floor(Math.random() * conversationalElements.length)];

            // Add at end of sentence (before punctuation)
            processedSentence = sentence.replace(/([.!?])$/, `, ${element}$1`);
        }

        processedSentences.push(processedSentence);
    });

    return processedSentences.join(' ');
}

/**
 * Add natural human inconsistencies
 */
function addNaturalHumanInconsistencies(text, aggressiveness) {
    let result = text;

    // Inconsistent contractions
    const contractionPairs = [
        ['do not', "don't"],
        ['will not', "won't"],
        ['cannot', "can't"],
        ['it is', "it's"],
        ['that is', "that's"],
        ['we are', "we're"],
        ['you are', "you're"]
    ];

    contractionPairs.forEach(([full, contracted]) => {
        const regex = new RegExp(`\\b${full}\\b`, 'gi');
        result = result.replace(regex, (match) => {
            // Randomly use contracted or full form
            return Math.random() < aggressiveness * 0.6 ? contracted : match;
        });
    });

    return result;
}

/**
 * Advanced semantic disruption to break AI patterns
 */
function advancedSemanticDisruption(text, aggressiveness, protectedSentences = [], aiAnalysis = {}) {
    let result = text;

    // Replace AI-specific patterns with more natural alternatives
    const aiPatternReplacements = {
        'in order to': ['to', 'so we can', 'to help'],
        'with regard to': ['about', 'concerning', 'regarding'],
        'in terms of': ['when it comes to', 'as for', 'regarding'],
        'as a result of': ['because of', 'due to', 'thanks to'],
        'it should be noted that': ['note that', 'keep in mind', 'remember'],
        'it is important to': ['you should', 'make sure to', 'be sure to'],
        'it is worth mentioning': ['also', 'plus', 'by the way']
    };

    Object.entries(aiPatternReplacements).forEach(([pattern, replacements]) => {
        const regex = new RegExp(pattern, 'gi');
        result = result.replace(regex, (match) => {
            if (Math.random() < aggressiveness * 0.7) {
                const replacement = replacements[Math.floor(Math.random() * replacements.length)];
                return replacement;
            }
            return match;
        });
    });

    return result;
}

/**
 * Main advanced humanization function for Netlify
 */
function advancedHumanization(text, options = {}) {
    const {
        aggressiveness = 0.7,
        maintainTone = true,
        targetDetection = 10,
        useModelBased = false, // Disabled for Netlify
        fallbackToPatterns = true
    } = options;

    if (!text || typeof text !== 'string' || !text.trim()) {
        return {
            text: text || '',
            method: 'none',
            success: false,
            error: 'Invalid input text'
        };
    }

    const startTime = Date.now();

    console.log(`🔧 Advanced pattern-based humanization (target: ≤${targetDetection}% AI detection)...`);

    // Enhanced AI pattern detection and dynamic aggressiveness adjustment
    const aiAnalysis = comprehensiveAIPatternAnalysis(text);
    const adjustedAggressiveness = dynamicAggressivenessCalculation(text, aggressiveness, aiAnalysis, targetDetection);

    const originalText = text;
    let result = text;

    console.log(`AI patterns detected: ${aiAnalysis.totalPatterns}, severity: ${aiAnalysis.severity}, adjusted aggressiveness: ${adjustedAggressiveness.toFixed(2)}`);

    // Phase 1: Text structure analysis
    const textAnalysis = analyzeTextStructure(result);
    const protectedSentences = []; // Simplified for Netlify
    const hesitationFrequency = 0.05; // 5% default frequency

    // Phase 2: Enhanced Sentence Restructuring
    result = enhancedSentenceRestructuring(result, textAnalysis, adjustedAggressiveness, protectedSentences);

    // Phase 3: Advanced Context-Aware Synonym Replacement
    result = advancedContextAwareSynonymReplacement(result, adjustedAggressiveness, protectedSentences);

    // Phase 4: Enhanced Perplexity and Burstiness
    result = enhancePerplexityAndBurstiness(result, textAnalysis, adjustedAggressiveness, protectedSentences);

    // Phase 5: Sophisticated Human Writing Pattern Injection
    result = sophisticatedHumanPatternInjection(result, adjustedAggressiveness, hesitationFrequency, protectedSentences);

    // Phase 6: Advanced Semantic Coherence Disruption
    result = advancedSemanticDisruption(result, adjustedAggressiveness, protectedSentences, aiAnalysis);

    const processingTime = Date.now() - startTime;

    return {
        text: result,
        method: 'advanced-pattern-based',
        success: true,
        originalLength: originalText.length,
        newLength: result.length,
        transformationRate: ((originalText.length - result.length) / originalText.length * 100).toFixed(2),
        processingTime,
        aiAnalysis,
        adjustedAggressiveness,
        targetDetection
    };
}

export {
    comprehensiveAIPatternAnalysis,
    dynamicAggressivenessCalculation,
    analyzeTextStructure,
    countFormalWords,
    countAITriggers,
    enhancedSentenceRestructuring,
    convertPassiveToActive,
    varyStartingWords,
    restructureSentenceOrder,
    addTransitionalPhrases,
    advancedContextAwareSynonymReplacement,
    enhancePerplexityAndBurstiness,
    sophisticatedHumanPatternInjection,
    addSophisticatedHesitationPatterns,
    addAuthenticConversationalElements,
    addNaturalHumanInconsistencies,
    advancedSemanticDisruption,
    advancedHumanization,
    AI_PATTERNS
};
