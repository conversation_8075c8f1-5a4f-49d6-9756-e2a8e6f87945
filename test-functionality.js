// Test Netlify functionality
import { handler } from './netlify/functions/process.js';

console.log('🧪 Testing Netlify Humanization Functionality');
console.log('='.repeat(50));

const testText = "The comprehensive analysis demonstrates that the implementation facilitates optimal performance. Furthermore, the system utilizes advanced algorithms to enhance user experience.";

async function testBasicFunctionality() {
    console.log('\n🔬 Testing basic functionality...');
    
    try {
        const event = {
            httpMethod: 'POST',
            body: JSON.stringify({
                text: testText,
                method: 'auto',
                aggressiveness: 0.7,
                targetDetection: 10
            })
        };

        const result = await handler(event, {});
        const response = JSON.parse(result.body);

        if (response.success) {
            console.log('✅ Basic functionality working');
            console.log(`📊 Method: ${response.method}`);
            console.log(`📊 Detection Score: ${response.detectionResult.score.toFixed(1)}%`);
            console.log(`⚡ Processing Time: ${response.processingTime}ms`);
            console.log(`🔄 Transformation Rate: ${response.transformationRate}%`);
            
            console.log('\n📖 Text Comparison:');
            console.log(`Original: "${testText.substring(0, 80)}..."`);
            console.log(`Modified: "${response.modifiedText.substring(0, 80)}..."`);
            
            return true;
        } else {
            console.log('❌ Basic functionality failed:', response.message);
            return false;
        }
    } catch (error) {
        console.error('❌ Test error:', error.message);
        return false;
    }
}

async function testDifferentMethods() {
    console.log('\n🔧 Testing different methods...');
    
    const methods = ['nltk', 'advanced', 'balanced'];
    
    for (const method of methods) {
        try {
            const event = {
                httpMethod: 'POST',
                body: JSON.stringify({
                    text: testText,
                    method: method,
                    aggressiveness: 0.7,
                    targetDetection: 10
                })
            };

            const result = await handler(event, {});
            const response = JSON.parse(result.body);

            if (response.success) {
                console.log(`✅ ${method.toUpperCase()} method working - Score: ${response.detectionResult.score.toFixed(1)}%`);
            } else {
                console.log(`❌ ${method.toUpperCase()} method failed: ${response.message}`);
            }
        } catch (error) {
            console.error(`❌ ${method.toUpperCase()} method error:`, error.message);
        }
    }
}

async function testCommercialMethod() {
    console.log('\n🏭 Testing commercial method...');
    
    try {
        const event = {
            httpMethod: 'POST',
            body: JSON.stringify({
                text: testText,
                method: 'commercial',
                aggressiveness: 0.9,
                targetDetection: 5,
                commercialGrade: true
            })
        };

        const result = await handler(event, {});
        const response = JSON.parse(result.body);

        if (response.success) {
            console.log('✅ Commercial method working');
            console.log(`📊 Detection Score: ${response.detectionResult.score.toFixed(1)}%`);
            console.log(`🏆 Quality Grade: ${response.commercialMetrics?.grade || 'N/A'}`);
            console.log(`🔄 Transformation Rate: ${response.transformationRate}%`);
        } else {
            console.log('❌ Commercial method failed:', response.message);
        }
    } catch (error) {
        console.error('❌ Commercial method error:', error.message);
    }
}

async function testErrorHandling() {
    console.log('\n🚨 Testing error handling...');
    
    try {
        const event = {
            httpMethod: 'POST',
            body: JSON.stringify({
                text: '',
                method: 'auto'
            })
        };

        const result = await handler(event, {});
        
        if (result.statusCode === 400) {
            console.log('✅ Error handling working - Empty text rejected');
        } else {
            console.log('❌ Error handling failed - Empty text not rejected');
        }
    } catch (error) {
        console.error('❌ Error handling test failed:', error.message);
    }
}

async function runTests() {
    try {
        const basicWorking = await testBasicFunctionality();
        
        if (basicWorking) {
            await testDifferentMethods();
            await testCommercialMethod();
        }
        
        await testErrorHandling();
        
        console.log('\n🎉 Testing completed!');
        console.log('='.repeat(50));
        
    } catch (error) {
        console.error('❌ Test suite failed:', error);
    }
}

runTests();
