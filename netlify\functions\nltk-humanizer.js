// netlify/functions/nltk-humanizer-clean.js
// Complete NLTK-inspired humanization algorithm for Netlify
// Self-contained ES module implementation matching localhost quality

/**
 * Enhanced synonym replacement system with contextual understanding
 */
function getAdvancedSynonyms(word, posTag, context = '') {
    const synonymDatabase = {
        'important': {
            casual: ['key', 'big', 'major', 'main', 'crucial'],
            formal: ['significant', 'vital', 'essential', 'critical']
        },
        'significant': {
            casual: ['big', 'major', 'key', 'important'],
            formal: ['substantial', 'considerable', 'notable', 'meaningful']
        },
        'excellent': {
            casual: ['great', 'awesome', 'amazing', 'fantastic'],
            formal: ['outstanding', 'exceptional', 'superior', 'remarkable']
        },
        'comprehensive': {
            casual: ['complete', 'full', 'thorough', 'detailed'],
            formal: ['extensive', 'exhaustive', 'all-encompassing', 'wide-ranging']
        },
        'sophisticated': {
            casual: ['advanced', 'complex', 'smart', 'clever'],
            formal: ['refined', 'elaborate', 'intricate', 'nuanced']
        },
        'utilize': {
            casual: ['use', 'apply', 'employ', 'work with'],
            formal: ['implement', 'deploy', 'leverage', 'harness']
        },
        'demonstrate': {
            casual: ['show', 'prove', 'display', 'reveal'],
            formal: ['illustrate', 'exhibit', 'manifest', 'exemplify']
        },
        'furthermore': {
            casual: ['also', 'plus', 'and', 'too'],
            formal: ['additionally', 'moreover', 'in addition', 'besides']
        },
        'consequently': {
            casual: ['so', 'thus', 'then', 'as a result'],
            formal: ['therefore', 'hence', 'accordingly', 'subsequently']
        },
        'exceptional': {
            casual: ['amazing', 'outstanding', 'great', 'fantastic'],
            formal: ['remarkable', 'extraordinary', 'superior', 'distinguished']
        }
    };

    const word_lower = word.toLowerCase();
    if (synonymDatabase[word_lower]) {
        const synonymEntry = synonymDatabase[word_lower];
        const synonyms = synonymEntry.casual || synonymEntry;
        return Array.isArray(synonyms) ?
               synonyms[Math.floor(Math.random() * synonyms.length)] :
               synonyms[Math.floor(Math.random() * synonyms.length)];
    }

    return getBasicSynonym(word_lower, posTag);
}

/**
 * Basic synonym database for fallback
 */
const SYNONYM_DATABASE = {
    'JJ': {
        'good': ['excellent', 'great', 'fine', 'wonderful', 'superb', 'outstanding', 'remarkable', 'fantastic', 'terrific', 'awesome'],
        'bad': ['terrible', 'awful', 'horrible', 'dreadful', 'poor', 'lousy', 'rotten', 'nasty', 'unpleasant', 'disappointing'],
        'big': ['large', 'huge', 'enormous', 'massive', 'gigantic', 'vast', 'immense', 'colossal', 'tremendous', 'substantial'],
        'small': ['tiny', 'little', 'minute', 'compact', 'petite', 'miniature', 'microscopic', 'diminutive', 'modest', 'limited'],
        'important': ['crucial', 'vital', 'essential', 'significant', 'critical', 'key', 'major', 'fundamental', 'primary', 'central'],
        'excellent': ['outstanding', 'superb', 'exceptional', 'remarkable', 'fantastic', 'wonderful', 'great', 'amazing', 'brilliant', 'magnificent'],
        'significant': ['important', 'major', 'substantial', 'considerable', 'notable', 'meaningful', 'crucial', 'vital', 'key', 'essential'],
        'comprehensive': ['complete', 'thorough', 'extensive', 'detailed', 'full', 'exhaustive', 'all-encompassing', 'wide-ranging', 'broad', 'inclusive'],
        'sophisticated': ['advanced', 'complex', 'refined', 'elaborate', 'intricate', 'nuanced', 'polished', 'cultured', 'elegant', 'developed'],
        'exceptional': ['outstanding', 'remarkable', 'extraordinary', 'superior', 'excellent', 'amazing', 'fantastic', 'incredible', 'phenomenal', 'superb']
    },
    'RB': {
        'very': ['extremely', 'incredibly', 'remarkably', 'exceptionally', 'particularly', 'especially', 'highly', 'quite', 'rather', 'really'],
        'quickly': ['rapidly', 'swiftly', 'speedily', 'hastily', 'promptly', 'immediately', 'instantly', 'fast', 'briskly', 'efficiently'],
        'effectively': ['efficiently', 'successfully', 'productively', 'competently', 'skillfully', 'capably', 'proficiently', 'adeptly', 'expertly', 'well'],
        'significantly': ['considerably', 'substantially', 'notably', 'markedly', 'dramatically', 'greatly', 'importantly', 'meaningfully', 'remarkably', 'extensively'],
        'furthermore': ['additionally', 'moreover', 'also', 'plus', 'besides', 'likewise', 'similarly', 'equally', 'too', 'as well'],
        'consequently': ['therefore', 'thus', 'hence', 'accordingly', 'so', 'as a result', 'subsequently', 'then', 'thereby', 'for this reason']
    },
    'VB': {
        'utilize': ['use', 'employ', 'apply', 'implement', 'deploy', 'leverage', 'harness', 'exploit', 'work with', 'make use of'],
        'demonstrate': ['show', 'display', 'exhibit', 'illustrate', 'reveal', 'present', 'prove', 'indicate', 'manifest', 'exemplify'],
        'implement': ['execute', 'carry out', 'put into practice', 'apply', 'deploy', 'install', 'establish', 'introduce', 'enact', 'realize'],
        'facilitate': ['help', 'assist', 'aid', 'support', 'enable', 'promote', 'encourage', 'ease', 'simplify', 'streamline'],
        'optimize': ['improve', 'enhance', 'refine', 'perfect', 'maximize', 'fine-tune', 'streamline', 'upgrade', 'boost', 'polish']
    },
    'NN': {
        'system': ['framework', 'structure', 'mechanism', 'setup', 'arrangement', 'organization', 'network', 'platform', 'infrastructure', 'architecture'],
        'approach': ['method', 'technique', 'strategy', 'way', 'manner', 'procedure', 'process', 'methodology', 'tactic', 'route'],
        'solution': ['answer', 'resolution', 'fix', 'remedy', 'response', 'result', 'outcome', 'way out', 'key', 'approach'],
        'performance': ['execution', 'operation', 'functioning', 'behavior', 'results', 'output', 'effectiveness', 'efficiency', 'capability', 'achievement'],
        'methodology': ['approach', 'method', 'technique', 'procedure', 'process', 'system', 'strategy', 'way', 'framework', 'protocol']
    }
};

/**
 * Get basic synonym fallback
 */
function getBasicSynonym(word, posTag) {
    const advanced = getAdvancedSynonyms(word, posTag);
    if (advanced && advanced !== word) {
        return advanced;
    }
    
    const basicReplacements = {
        'furthermore': 'also',
        'moreover': 'plus',
        'additionally': 'also',
        'consequently': 'so',
        'therefore': 'so',
        'utilize': 'use',
        'implement': 'do',
        'facilitate': 'help',
        'demonstrate': 'show',
        'comprehensive': 'complete',
        'sophisticated': 'advanced',
        'exceptional': 'great'
    };
    
    return basicReplacements[word.toLowerCase()] || word;
}

/**
 * Get synonyms based on POS tag
 */
function getSynonymsByPOS(word, posTag) {
    let posCategory = 'NN';
    if (posTag.startsWith('JJ')) posCategory = 'JJ';
    else if (posTag.startsWith('RB')) posCategory = 'RB';
    else if (posTag.startsWith('VB')) posCategory = 'VB';

    const categoryDatabase = SYNONYM_DATABASE[posCategory] || {};
    return categoryDatabase[word] || [];
}

/**
 * Check if word is too common to replace
 */
function isCommonWord(word) {
    const commonWords = new Set([
        'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by',
        'from', 'up', 'about', 'into', 'through', 'during', 'before', 'after', 'above', 'below',
        'between', 'among', 'under', 'over', 'is', 'are', 'was', 'were', 'be', 'been', 'being',
        'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may',
        'might', 'must', 'can', 'shall', 'this', 'that', 'these', 'those', 'i', 'you', 'he',
        'she', 'it', 'we', 'they', 'me', 'him', 'her', 'us', 'them', 'my', 'your', 'his',
        'her', 'its', 'our', 'their'
    ]);

    return commonWords.has(word.toLowerCase());
}

/**
 * Select best synonym based on frequency and context
 */
function selectBestSynonym(synonyms, originalWord) {
    if (synonyms.length === 0) return originalWord;
    const topSynonyms = synonyms.slice(0, Math.min(3, synonyms.length));
    return topSynonyms[Math.floor(Math.random() * topSynonyms.length)];
}

/**
 * Preserve original capitalization pattern
 */
function preserveCapitalization(original, replacement) {
    if (!original || !replacement) return replacement;

    if (original === original.toUpperCase()) {
        return replacement.toUpperCase();
    }

    if (original[0] === original[0].toUpperCase()) {
        return replacement.charAt(0).toUpperCase() + replacement.slice(1).toLowerCase();
    }

    return replacement.toLowerCase();
}

/**
 * Determine POS tag for a word using pattern matching
 */
function determinePOSTag(word) {
    if (word.match(/^(very|quite|rather|extremely|highly|incredibly|amazingly|particularly|especially|remarkably)$/)) return 'RB';
    if (word.match(/(ful|less|ous|ive|able|ible|al|ic|ed|ing)$/)) return 'JJ';
    if (word.match(/^(good|bad|great|small|large|big|new|old|high|low|important|different|possible|available|necessary|special|certain|easy|hard|difficult|simple|complex|fast|slow|quick|strong|weak|beautiful|ugly|happy|sad|young|early|late|recent)$/)) return 'JJ';

    if (word.match(/(ly|ward|wise)$/)) return 'RB';

    if (word.match(/(ed|ing|s)$/)) return 'VB';
    if (word.match(/^(is|are|was|were|be|been|being|have|has|had|having|do|does|did|doing|will|would|could|should|might|may|can|must|shall|ought|need|dare|used)$/)) return 'VB';

    return 'NN';
}

/**
 * Simple POS tagging for JavaScript
 */
function getPOSTags(sentence) {
    const words = sentence.match(/\w+/g) || [];
    const tags = [];

    for (const word of words) {
        const pos = determinePOSTag(word.toLowerCase());
        tags.push({ word, pos });
    }

    return tags;
}

/**
 * Replace word based on POS tag
 */
function replaceWordWithPOS(word, posTag, aggressiveness) {
    if (word.length <= 2 || isCommonWord(word)) {
        return word;
    }

    let replacementProbability = 0.3;

    if (posTag.startsWith('JJ')) {
        replacementProbability = 0.6 * aggressiveness;
    } else if (posTag.startsWith('RB')) {
        replacementProbability = 0.5 * aggressiveness;
    } else if (posTag.startsWith('VB')) {
        replacementProbability = 0.4 * aggressiveness;
    } else {
        replacementProbability = 0.2 * aggressiveness;
    }

    if (Math.random() > replacementProbability) {
        return word;
    }

    const synonyms = getSynonymsByPOS(word.toLowerCase(), posTag);

    if (synonyms.length === 0) {
        return word;
    }

    const chosenSynonym = selectBestSynonym(synonyms, word);
    return preserveCapitalization(word, chosenSynonym);
}

/**
 * Clean symbols and spacing
 */
function cleanSymbols(text) {
    // First, ensure proper spacing between words
    let cleaned = text.replace(/([a-zA-Z])([A-Z])/g, '$1 $2'); // Add space before capital letters
    cleaned = cleaned.replace(/([a-z])([A-Z])/g, '$1 $2'); // Add space between lowercase and uppercase

    // Clean up multiple spaces
    cleaned = cleaned.replace(/\s+/g, ' ');

    // Fix spacing around punctuation
    cleaned = cleaned.replace(/\s+([.!?,:;])/g, '$1'); // Remove space before punctuation
    cleaned = cleaned.replace(/([.!?])\s*([A-Z])/g, '$1 $2'); // Ensure space after sentence endings
    cleaned = cleaned.replace(/([.!?])([A-Z])/g, '$1 $2'); // Fix missing space after sentence endings
    cleaned = cleaned.replace(/,([A-Za-z])/g, ', $1'); // Fix missing space after commas

    return cleaned.trim();
}

/**
 * Apply contractions to make text more casual
 */
function applyContractions(text) {
    const contractions = {
        'do not': "don't",
        'does not': "doesn't",
        'did not': "didn't",
        'will not': "won't",
        'would not': "wouldn't",
        'could not': "couldn't",
        'should not': "shouldn't",
        'cannot': "can't",
        'it is': "it's",
        'that is': "that's",
        'there is': "there's",
        'we are': "we're",
        'they are': "they're",
        'you are': "you're",
        'I am': "I'm",
        'he is': "he's",
        'she is': "she's"
    };

    let result = text;
    Object.entries(contractions).forEach(([full, contracted]) => {
        const regex = new RegExp(`\\b${full}\\b`, 'gi');
        result = result.replace(regex, (match) => {
            return Math.random() < 0.3 ? contracted : match;
        });
    });

    return result;
}

/**
 * Add casual qualifiers to make text more human-like
 */
function addCasualQualifiers(text, frequency = 0.1) {
    const qualifiers = [
        'kind of', 'sort of', 'pretty much', 'basically', 'essentially',
        'generally', 'typically', 'usually', 'often', 'sometimes',
        'perhaps', 'maybe', 'probably', 'likely', 'possibly'
    ];

    let result = text;

    result = result.replace(/\b(very|quite|really|extremely|highly|incredibly|remarkably|particularly|especially|significantly)\s+(\w+)/g, (match, intensifier, word) => {
        if (Math.random() < frequency) {
            const qualifier = qualifiers[Math.floor(Math.random() * qualifiers.length)];
            return `${qualifier} ${intensifier} ${word}`;
        }
        return match;
    });

    result = result.replace(/\b(This|That|It|The system|The approach|The method|The result)\s+(is|was|will|can|should|would|could)\b/g, (match, subject, verb) => {
        if (Math.random() < frequency * 0.5) {
            const qualifier = qualifiers[Math.floor(Math.random() * qualifiers.length)];
            return `${subject} ${qualifier} ${verb}`;
        }
        return match;
    });

    return result;
}

/**
 * Basic humanization with simple transformations
 */
function applyBasicHumanization(text, options = {}) {
    const { aggressiveness = 0.7 } = options;

    let result = text;

    result = applyContractions(result);

    if (aggressiveness > 0.5) {
        result = addCasualQualifiers(result, aggressiveness * 0.15);
    }

    const simpleReplacements = {
        'furthermore': 'and',
        'moreover': 'plus',
        'additionally': 'also',
        'consequently': 'so',
        'therefore': 'so',
        'utilize': 'use',
        'implement': 'do',
        'facilitate': 'help'
    };

    Object.entries(simpleReplacements).forEach(([formal, casual]) => {
        const regex = new RegExp(`\\b${formal}\\b`, 'gi');
        result = result.replace(regex, casual);
    });

    return result;
}

/**
 * Main NLTK-inspired processing function
 */
function processTextWithNLTKApproach(text, options = {}) {
    const { aggressiveness = 0.7 } = options;

    const newlinePlaceholder = "庄周";
    const textWithPlaceholders = text.replace(/\n/g, newlinePlaceholder);

    const sentences = textWithPlaceholders.match(/[^.!?]*[.!?]+\s*|[^.!?]+$/g) || [textWithPlaceholders];

    const humanizedSentences = [];

    for (const sentence of sentences) {
        if (!sentence.trim()) {
            humanizedSentences.push(sentence);
            continue;
        }

        const tokens = sentence.match(/\w+|[^\w\s]+/g) || [];
        const posTags = getPOSTags(sentence);

        const humanizedTokens = [];
        let wordIndex = 0;

        for (let i = 0; i < tokens.length; i++) {
            const token = tokens[i];
            const nextToken = tokens[i + 1];

            if (/^\w+$/.test(token)) {
                const posTag = posTags[wordIndex] || { word: token, pos: 'NN' };
                const humanizedWord = replaceWordWithPOS(token, posTag.pos, aggressiveness);
                humanizedTokens.push(humanizedWord);

                // Add space after word if next token is also a word
                if (nextToken && /^\w+$/.test(nextToken)) {
                    humanizedTokens.push(' ');
                }

                wordIndex++;
            } else {
                humanizedTokens.push(token);
            }
        }

        let humanizedSentence = humanizedTokens.join('');
        humanizedSentence = cleanSymbols(humanizedSentence);
        humanizedSentences.push(humanizedSentence);
    }

    let result = humanizedSentences.join('');
    result = result.replace(new RegExp(newlinePlaceholder, 'g'), '\n');

    return result.trim();
}

/**
 * Performance tiers configuration
 */
const PERFORMANCE_TIERS = {
    free: {
        processingDelay: 500,
        batchSize: 20,
        cacheEnabled: false,
        parallelProcessing: false,
        maxCacheSize: 0,
        useOptimizedRegex: false,
        usePrecomputedSets: false,
        algorithmComplexity: 'basic'
    },
    premium: {
        processingDelay: 0,
        batchSize: 100,
        cacheEnabled: true,
        parallelProcessing: true,
        maxCacheSize: 1000,
        useOptimizedRegex: true,
        usePrecomputedSets: true,
        useLRUCache: true,
        algorithmComplexity: 'optimized'
    },
    admin: {
        processingDelay: 0,
        batchSize: 200,
        cacheEnabled: true,
        parallelProcessing: true,
        optimizedAlgorithms: true,
        maxCacheSize: 2000,
        useOptimizedRegex: true,
        usePrecomputedSets: true,
        useLRUCache: true,
        useAdvancedOptimizations: true,
        algorithmComplexity: 'maximum'
    }
};

/**
 * Detect user tier from session or request context
 */
function getUserTier(options = {}) {
    if (options.userTier) {
        return options.userTier;
    }

    if (options.user) {
        const subscriptionTier = options.user.subscriptionTier || 'free';
        const userRole = options.user.role || 'user';

        if (userRole === 'admin' || userRole === 'owner') return 'admin';
        if (subscriptionTier.includes('premium') || subscriptionTier.includes('pro')) return 'premium';
    }

    return 'free';
}

/**
 * Main humanization function with tier support
 */
async function humanizeWithNLTKApproach(text, options = {}) {
    const {
        aggressiveness = 0.7,
        maintainTone = true,
        targetDetection = 10,
        useAdvancedSynonyms = true,
        user = null
    } = options;

    if (!text || typeof text !== 'string' || !text.trim()) {
        return {
            success: false,
            error: 'Input text is required and must be a non-empty string'
        };
    }

    const startTime = Date.now();
    const userTier = getUserTier({ user, ...options });
    const performanceConfig = PERFORMANCE_TIERS[userTier];

    try {
        console.log(`🔬 Starting NLTK-inspired humanization (${userTier} tier)...`);

        if (performanceConfig.processingDelay > 0) {
            await new Promise(resolve => setTimeout(resolve, performanceConfig.processingDelay));
        }

        const humanizedText = processTextWithNLTKApproach(text, {
            aggressiveness,
            maintainTone,
            useAdvancedSynonyms
        });

        const processingTime = Date.now() - startTime;

        return {
            success: true,
            text: humanizedText,
            originalText: text,
            method: 'nltk-inspired',
            processingTime,
            originalLength: text.length,
            newLength: humanizedText.length,
            transformationRate: ((text.length - humanizedText.length) / text.length * 100).toFixed(2),
            userTier,
            performanceConfig: {
                tier: userTier,
                processingDelay: performanceConfig.processingDelay,
                cacheEnabled: performanceConfig.cacheEnabled,
                parallelProcessing: performanceConfig.parallelProcessing
            }
        };
    } catch (error) {
        console.error('NLTK humanization error:', error);
        return {
            success: false,
            error: error.message || 'Failed to process text with NLTK approach',
            originalText: text,
            processingTime: Date.now() - startTime
        };
    }
}

/**
 * Netlify function handler
 */
export const handler = async (event, context) => {
    const headers = {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Content-Type': 'application/json',
    };

    if (event.httpMethod === 'OPTIONS') {
        return {
            statusCode: 200,
            headers,
            body: '',
        };
    }

    if (event.httpMethod !== 'POST') {
        return {
            statusCode: 405,
            headers,
            body: JSON.stringify({
                success: false,
                error: `Method ${event.httpMethod} Not Allowed`
            }),
        };
    }

    try {
        const { text, options = {} } = JSON.parse(event.body);

        if (!text || typeof text !== 'string') {
            return {
                statusCode: 400,
                headers,
                body: JSON.stringify({
                    success: false,
                    error: 'Text is required and must be a string'
                })
            };
        }

        const startTime = Date.now();
        const result = await humanizeWithNLTKApproach(text, options);
        const totalProcessingTime = Date.now() - startTime;

        return {
            statusCode: 200,
            headers,
            body: JSON.stringify({
                ...result,
                totalProcessingTime,
                method: 'nltk-humanizer',
                timestamp: new Date().toISOString()
            })
        };

    } catch (error) {
        console.error('Netlify function error:', error);
        return {
            statusCode: 500,
            headers,
            body: JSON.stringify({
                success: false,
                error: 'Internal server error',
                message: error.message,
                timestamp: new Date().toISOString()
            })
        };
    }
};

export {
    getAdvancedSynonyms,
    getSynonymsByPOS,
    isCommonWord,
    selectBestSynonym,
    preserveCapitalization,
    determinePOSTag,
    getPOSTags,
    replaceWordWithPOS,
    cleanSymbols,
    applyBasicHumanization,
    processTextWithNLTKApproach,
    humanizeWithNLTKApproach,
    getUserTier,
    PERFORMANCE_TIERS,
    SYNONYM_DATABASE
};
