// Test to compare quality between old and new Netlify implementation
import { handler } from './netlify/functions/process.js';

const testText = "The comprehensive analysis demonstrates that the implementation facilitates optimal performance. Furthermore, the system utilizes advanced algorithms to enhance user experience significantly.";

console.log('🔬 Quality Comparison: Localhost Services vs Previous Implementation');
console.log('='.repeat(70));

async function testAdvancedMethod() {
    console.log('\n🚀 Testing Advanced Method (using localhost services):');
    console.log('-'.repeat(50));
    
    try {
        const event = {
            httpMethod: 'POST',
            body: JSON.stringify({
                text: testText,
                method: 'advanced',
                aggressiveness: 0.7,
                targetDetection: 10
            })
        };

        const result = await handler(event, {});
        const response = JSON.parse(result.body);

        if (response.success) {
            console.log(`✅ Success: ${response.detectionResult.score.toFixed(1)}% AI detection`);
            console.log(`🔄 Transformation: ${response.transformationRate}%`);
            console.log(`⚡ Speed: ${response.processingTime}ms`);
            
            console.log('\n📖 Text Transformation:');
            console.log(`📝 Original: "${testText}"`);
            console.log(`🎯 Modified: "${response.modifiedText}"`);
            
            // Highlight the differences
            const changes = findTextDifferences(testText, response.modifiedText);
            if (changes.length > 0) {
                console.log('\n🔍 Key Changes Detected:');
                changes.forEach((change, i) => {
                    console.log(`   ${i + 1}. "${change.from}" → "${change.to}"`);
                });
            }
            
            return {
                detection: response.detectionResult.score,
                transformation: parseFloat(response.transformationRate),
                speed: response.processingTime,
                hasChanges: changes.length > 0
            };
        } else {
            console.log('❌ Failed:', response.message);
            return null;
        }
    } catch (error) {
        console.error('❌ Error:', error.message);
        return null;
    }
}

async function testMainService() {
    console.log('\n🎯 Testing Main Humanization Service (localhost):');
    console.log('-'.repeat(50));
    
    try {
        const event = {
            httpMethod: 'POST',
            body: JSON.stringify({
                text: testText,
                method: 'humanizeText',
                aggressiveness: 0.7,
                targetDetection: 10
            })
        };

        const result = await handler(event, {});
        const response = JSON.parse(result.body);

        if (response.success) {
            console.log(`✅ Success: ${response.detectionResult.score.toFixed(1)}% AI detection`);
            console.log(`🔄 Transformation: ${response.transformationRate}%`);
            console.log(`⚡ Speed: ${response.processingTime}ms`);
            console.log(`🔧 Method Used: ${response.method}`);
            
            console.log('\n📖 Text Transformation:');
            console.log(`📝 Original: "${testText}"`);
            console.log(`🎯 Modified: "${response.modifiedText}"`);
            
            return {
                detection: response.detectionResult.score,
                transformation: parseFloat(response.transformationRate),
                speed: response.processingTime,
                method: response.method
            };
        } else {
            console.log('❌ Failed:', response.message);
            return null;
        }
    } catch (error) {
        console.error('❌ Error:', error.message);
        return null;
    }
}

function findTextDifferences(original, modified) {
    const originalWords = original.split(/\s+/);
    const modifiedWords = modified.split(/\s+/);
    const changes = [];
    
    const maxLength = Math.max(originalWords.length, modifiedWords.length);
    
    for (let i = 0; i < maxLength; i++) {
        const origWord = originalWords[i] || '';
        const modWord = modifiedWords[i] || '';
        
        if (origWord !== modWord && origWord && modWord) {
            changes.push({ from: origWord, to: modWord });
        }
    }
    
    return changes.slice(0, 5); // Show first 5 changes
}

async function runComparison() {
    console.log(`📝 Test Text: "${testText}"`);
    console.log(`📏 Length: ${testText.length} characters`);
    
    const advancedResult = await testAdvancedMethod();
    const mainServiceResult = await testMainService();
    
    console.log('\n📊 COMPARISON SUMMARY:');
    console.log('='.repeat(70));
    
    if (advancedResult) {
        console.log(`🔧 Advanced Method:`);
        console.log(`   AI Detection: ${advancedResult.detection.toFixed(1)}%`);
        console.log(`   Transformation: ${advancedResult.transformation}%`);
        console.log(`   Speed: ${advancedResult.speed}ms`);
        console.log(`   Text Changes: ${advancedResult.hasChanges ? '✅ Yes' : '❌ No'}`);
    }
    
    if (mainServiceResult) {
        console.log(`🎯 Main Service:`);
        console.log(`   AI Detection: ${mainServiceResult.detection.toFixed(1)}%`);
        console.log(`   Transformation: ${mainServiceResult.transformation}%`);
        console.log(`   Speed: ${mainServiceResult.speed}ms`);
        console.log(`   Method: ${mainServiceResult.method}`);
    }
    
    console.log('\n🎉 CONCLUSION:');
    if (advancedResult && advancedResult.hasChanges) {
        console.log('✅ Netlify implementation now uses actual localhost services!');
        console.log('✅ Text transformations are working properly!');
        console.log('✅ AI detection scores are realistic and effective!');
    } else {
        console.log('⚠️  Some issues may still exist with text transformation');
    }
}

runComparison();
