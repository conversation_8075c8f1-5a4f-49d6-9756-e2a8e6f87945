/**
 * Test script to verify ES modules work in Netlify functions
 * Tests the converted ES module implementations
 */

// Test ES module imports
async function testESModuleImports() {
    console.log('🧪 Testing ES Module Imports...\n');

    try {
        // Test NLTK humanizer ES module import
        console.log('📦 Testing NLTK humanizer import...');
        const nltkModule = await import('./netlify/functions/nltk-humanizer.js');
        console.log('✅ NLTK module imported successfully');
        console.log('- humanizeWithNLTKApproach:', typeof nltkModule.humanizeWithNLTKApproach === 'function');
        console.log('- applyBasicHumanization:', typeof nltkModule.applyBasicHumanization === 'function');
        console.log('- PERFORMANCE_TIERS:', typeof nltkModule.PERFORMANCE_TIERS === 'object');

        // Test text transformations ES module import
        console.log('\n📦 Testing text transformations import...');
        const textModule = await import('./netlify/functions/text-transformations.js');
        console.log('✅ Text transformations module imported successfully');
        console.log('- balancedHumanization:', typeof textModule.balancedHumanization === 'function');
        console.log('- qualityCheck:', typeof textModule.qualityCheck === 'function');
        console.log('- addControlledMistakes:', typeof textModule.addControlledMistakes === 'function');

        // Test process function ES module import
        console.log('\n📦 Testing process function import...');
        const processModule = await import('./netlify/functions/process.js');
        console.log('✅ Process function module imported successfully');
        console.log('- handler:', typeof processModule.handler === 'function');

        return { nltkModule, textModule, processModule };

    } catch (error) {
        console.error('❌ ES module import failed:', error.message);
        console.error('Stack trace:', error.stack);
        throw error;
    }
}

// Test NLTK humanization with ES modules
async function testNLTKHumanizationES() {
    console.log('\n🔬 Testing NLTK Humanization with ES Modules...\n');

    try {
        const { nltkModule } = await testESModuleImports();
        const { humanizeWithNLTKApproach } = nltkModule;

        const testText = "Furthermore, this sophisticated system demonstrates exceptional performance and utilizes advanced methodologies to achieve optimal results.";
        
        console.log('📝 Input:', testText);
        console.log('\n⚡ Processing with ES module NLTK approach...\n');

        const startTime = Date.now();
        const result = await humanizeWithNLTKApproach(testText, {
            aggressiveness: 0.8,
            targetDetection: 10,
            userTier: 'premium',
            user: { subscriptionTier: 'premium', role: 'user' }
        });
        const processingTime = Date.now() - startTime;

        console.log('✅ ES module NLTK processing completed!');
        console.log('📊 Success:', result.success);
        console.log('⏱️  Processing Time:', processingTime + 'ms');
        console.log('🎯 User Tier:', result.userTier);
        
        if (result.success) {
            console.log('\n📝 Humanized Output:');
            console.log(result.text);
            console.log('\n📈 Stats:');
            console.log('- Original Length:', result.originalLength);
            console.log('- New Length:', result.newLength);
            console.log('- Transformation Rate:', result.transformationRate + '%');
        } else {
            console.log('❌ Error:', result.error);
        }

        return result;

    } catch (error) {
        console.error('❌ ES module NLTK test failed:', error.message);
        throw error;
    }
}

// Test full Netlify function with ES modules
async function testNetlifyFunctionES() {
    console.log('\n🧪 Testing Full Netlify Function with ES Modules...\n');

    try {
        const { processModule } = await testESModuleImports();
        const { handler } = processModule;

        // Create a mock event
        const mockEvent = {
            httpMethod: 'POST',
            body: JSON.stringify({
                text: "This is a comprehensive test of the advanced artificial intelligence system that demonstrates remarkable capabilities and utilizes sophisticated algorithms to generate comprehensive responses.",
                styleStrength: 70
            })
        };

        const mockContext = {};

        console.log('📝 Input text:', JSON.parse(mockEvent.body).text);
        console.log('🎯 Style strength:', JSON.parse(mockEvent.body).styleStrength);
        console.log('\n⚡ Processing with ES modules...\n');

        const startTime = Date.now();
        const result = await handler(mockEvent, mockContext);
        const processingTime = Date.now() - startTime;

        console.log('✅ ES module function executed successfully!');
        console.log('📊 Status Code:', result.statusCode);
        console.log('⏱️  Total Processing Time:', processingTime + 'ms');

        if (result.statusCode === 200) {
            const responseData = JSON.parse(result.body);
            console.log('\n📤 Response Data:');
            console.log('- Success:', responseData.success);
            console.log('- Method:', responseData.method);
            console.log('- User Tier:', responseData.userTier);
            console.log('- Processing Time:', responseData.processingTime + 'ms');
            console.log('- Original Length:', responseData.originalLength);
            console.log('- New Length:', responseData.newLength);
            console.log('- Transformation Rate:', responseData.transformationRate + '%');
            
            if (responseData.modifiedText) {
                console.log('\n📝 Humanized Output:');
                console.log(responseData.modifiedText);
            }
        } else {
            console.log('❌ Error Response:', result.body);
        }

        return result;

    } catch (error) {
        console.error('❌ ES module function test failed:', error.message);
        console.error('Stack trace:', error.stack);
        throw error;
    }
}

// Run all ES module tests
async function runESModuleTests() {
    console.log('🚀 GhostLayer Netlify ES Modules Test Suite\n');
    console.log('=' .repeat(60));
    
    try {
        // Test 1: ES module imports
        await testESModuleImports();
        
        // Test 2: NLTK humanizer with ES modules
        await testNLTKHumanizationES();
        
        // Test 3: Full Netlify function with ES modules
        await testNetlifyFunctionES();
        
        console.log('\n' + '='.repeat(60));
        console.log('🎉 All ES module tests completed successfully!');
        console.log('\n💡 ES Module Conversion Complete:');
        console.log('1. ✅ NLTK Humanizer: CommonJS → ES modules');
        console.log('2. ✅ Text Transformations: CommonJS → ES modules');
        console.log('3. ✅ Process Function: CommonJS → ES modules');
        console.log('4. ✅ All imports/exports working correctly');
        console.log('\n🚀 Ready for Netlify deployment with ES modules!');

    } catch (error) {
        console.error('\n❌ ES module tests failed:', error.message);
        console.log('\n💡 Troubleshooting:');
        console.log('1. Ensure all files use ES module syntax (import/export)');
        console.log('2. Check that file extensions include .js in imports');
        console.log('3. Verify Netlify supports ES modules in functions');
        process.exit(1);
    }
}

// Execute tests
runESModuleTests().catch(console.error);
