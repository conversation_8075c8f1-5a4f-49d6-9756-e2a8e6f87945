/**
 * Test script for Netlify humanization implementation
 * Validates that all components work correctly and achieve quality targets
 */

import { handler } from './netlify/functions/process.js';

// Test content with varying complexity levels
const testContent = {
    simple: "The comprehensive analysis demonstrates that the implementation facilitates optimal performance. Furthermore, the system utilizes advanced algorithms to enhance user experience.",
    
    technical: "The API implementation leverages modern frameworks to optimize database queries. The configuration utilizes advanced caching mechanisms to enhance performance significantly.",
    
    formal: "Executive Summary\n\nThis document presents a comprehensive analysis of the proposed methodology. The research demonstrates significant improvements in operational efficiency. Furthermore, the implementation facilitates enhanced user engagement through innovative approaches.",
    
    academic: "Research indicates that the proposed methodology demonstrates significant improvements in performance metrics. Studies have shown that the implementation facilitates enhanced user engagement through innovative approaches. The analysis reveals substantial benefits in operational efficiency."
};

/**
 * Test different humanization methods
 */
async function testHumanizationMethods() {
    console.log('🧪 Testing Netlify Humanization Implementation');
    console.log('='.repeat(60));

    const methods = ['auto', 'advanced', 'commercial', 'nltk', 'balanced'];
    const testText = testContent.simple;

    for (const method of methods) {
        console.log(`\n🔬 Testing ${method.toUpperCase()} method:`);
        console.log('-'.repeat(40));

        try {
            const event = {
                httpMethod: 'POST',
                body: JSON.stringify({
                    text: testText,
                    method: method,
                    aggressiveness: 0.7,
                    targetDetection: method === 'commercial' ? 5 : 10
                })
            };

            const result = await handler(event, {});
            const response = JSON.parse(result.body);

            if (response.success) {
                console.log(`✅ ${method} method successful`);
                console.log(`📊 Detection Score: ${response.detectionResult.score.toFixed(1)}%`);
                console.log(`⚡ Processing Time: ${response.processingTime}ms`);
                console.log(`🔄 Transformation Rate: ${response.transformationRate}%`);
                console.log(`📝 Content Type: ${response.contentAnalysis.type}`);
                
                // Show text transformation sample
                const original = testText.substring(0, 100);
                const modified = response.modifiedText.substring(0, 100);
                console.log(`\n📖 Original: "${original}..."`);
                console.log(`📝 Modified: "${modified}..."`);
                
                // Validate quality targets
                const detectionScore = response.detectionResult.score;
                const targetMet = method === 'commercial' ? detectionScore <= 5 : detectionScore <= 10;
                console.log(`🎯 Target Met: ${targetMet ? '✅' : '❌'} (${detectionScore.toFixed(1)}% ≤ ${method === 'commercial' ? 5 : 10}%)`);
                
            } else {
                console.log(`❌ ${method} method failed: ${response.message}`);
            }
        } catch (error) {
            console.error(`❌ Error testing ${method} method:`, error.message);
        }
    }
}

/**
 * Test style integration
 */
async function testStyleIntegration() {
    console.log('\n\n🎨 Testing Style Integration');
    console.log('='.repeat(60));

    const styles = [
        { name: 'casual', strength: 70 },
        { name: 'professional', strength: 80 },
        { name: 'academic', strength: 60 }
    ];

    for (const style of styles) {
        console.log(`\n🎭 Testing ${style.name} style (${style.strength}% strength):`);
        console.log('-'.repeat(40));

        try {
            const event = {
                httpMethod: 'POST',
                body: JSON.stringify({
                    text: testContent.simple,
                    method: 'balanced',
                    styleProfile: style,
                    styleStrength: style.strength,
                    aggressiveness: 0.6
                })
            };

            const result = await handler(event, {});
            const response = JSON.parse(result.body);

            if (response.success) {
                console.log(`✅ ${style.name} style applied successfully`);
                console.log(`📊 Detection Score: ${response.detectionResult.score.toFixed(1)}%`);
                console.log(`⚡ Processing Time: ${response.processingTime}ms`);
                
                // Show style transformation
                const modified = response.modifiedText.substring(0, 150);
                console.log(`📝 Styled Text: "${modified}..."`);
            } else {
                console.log(`❌ ${style.name} style failed: ${response.message}`);
            }
        } catch (error) {
            console.error(`❌ Error testing ${style.name} style:`, error.message);
        }
    }
}

/**
 * Test content type handling
 */
async function testContentTypes() {
    console.log('\n\n📄 Testing Content Type Handling');
    console.log('='.repeat(60));

    const contentTypes = Object.keys(testContent);

    for (const type of contentTypes) {
        console.log(`\n📋 Testing ${type} content:`);
        console.log('-'.repeat(40));

        try {
            const event = {
                httpMethod: 'POST',
                body: JSON.stringify({
                    text: testContent[type],
                    method: 'auto',
                    aggressiveness: 0.7,
                    targetDetection: 10
                })
            };

            const result = await handler(event, {});
            const response = JSON.parse(result.body);

            if (response.success) {
                console.log(`✅ ${type} content processed successfully`);
                console.log(`📊 Detected Type: ${response.contentAnalysis.type}`);
                console.log(`🔧 Strategy: ${response.contentAnalysis.strategy}`);
                console.log(`🛡️ Protected Sentences: ${response.contentAnalysis.protectedSentences}`);
                console.log(`📊 Detection Score: ${response.detectionResult.score.toFixed(1)}%`);
                console.log(`🔄 Transformation Rate: ${response.transformationRate}%`);
            } else {
                console.log(`❌ ${type} content failed: ${response.message}`);
            }
        } catch (error) {
            console.error(`❌ Error testing ${type} content:`, error.message);
        }
    }
}

/**
 * Test error handling
 */
async function testErrorHandling() {
    console.log('\n\n🚨 Testing Error Handling');
    console.log('='.repeat(60));

    const errorTests = [
        { name: 'Empty text', body: { text: '', method: 'auto' } },
        { name: 'Invalid method', body: { text: 'Test text', method: 'invalid' } },
        { name: 'Invalid style strength', body: { text: 'Test text', styleStrength: 150 } }
    ];

    for (const test of errorTests) {
        console.log(`\n⚠️ Testing ${test.name}:`);
        console.log('-'.repeat(40));

        try {
            const event = {
                httpMethod: 'POST',
                body: JSON.stringify(test.body)
            };

            const result = await handler(event, {});
            const response = JSON.parse(result.body);

            if (result.statusCode === 400) {
                console.log(`✅ Error handled correctly: ${response.message}`);
            } else {
                console.log(`❌ Unexpected response: ${result.statusCode}`);
            }
        } catch (error) {
            console.error(`❌ Error in error test:`, error.message);
        }
    }
}

/**
 * Run all tests
 */
async function runAllTests() {
    try {
        await testHumanizationMethods();
        await testStyleIntegration();
        await testContentTypes();
        await testErrorHandling();
        
        console.log('\n\n🎉 All tests completed!');
        console.log('='.repeat(60));
        console.log('✅ Netlify implementation validation finished');
        
    } catch (error) {
        console.error('❌ Test suite failed:', error);
    }
}

// Run tests if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
    runAllTests();
}

export { runAllTests, testHumanizationMethods, testStyleIntegration, testContentTypes, testErrorHandling };
