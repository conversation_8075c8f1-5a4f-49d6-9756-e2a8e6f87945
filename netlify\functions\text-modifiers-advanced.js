/**
 * Advanced Text Modifiers for Netlify
 * Complete port of localhost textModifiers.js functionality
 * Provides sophisticated text transformation techniques
 */

// Common words to skip during processing
const COMMON_WORDS_TO_SKIP = new Set([
    'the', 'be', 'to', 'of', 'and', 'a', 'in', 'that', 'have', 'i', 'it', 'for', 'not', 'on', 'with',
    'he', 'as', 'you', 'do', 'at', 'this', 'but', 'his', 'by', 'from', 'they', 'she', 'or', 'an',
    'will', 'my', 'one', 'all', 'would', 'there', 'their', 'what', 'so', 'up', 'out', 'if', 'about',
    'who', 'get', 'which', 'go', 'me', 'when', 'make', 'can', 'like', 'time', 'no', 'just', 'him',
    'know', 'take', 'people', 'into', 'year', 'your', 'good', 'some', 'could', 'them', 'see', 'other',
    'than', 'then', 'now', 'look', 'only', 'come', 'its', 'over', 'think', 'also', 'back', 'after',
    'use', 'two', 'how', 'our', 'work', 'first', 'well', 'way', 'even', 'new', 'want', 'because',
    'any', 'these', 'give', 'day', 'most', 'us', 'is', 'was', 'are', 'been', 'has', 'had', 'were',
    'said', 'each', 'which', 'their', 'said', 'them', 'she', 'many', 'some', 'very', 'when', 'much',
    'before', 'right', 'too', 'means', 'old', 'any', 'same', 'tell', 'boy', 'follow', 'came', 'want',
    'show', 'also', 'around', 'farm', 'three', 'small', 'set', 'put', 'end', 'why', 'again', 'turn',
    'here', 'off', 'went', 'old', 'number', 'great', 'tell', 'men', 'say', 'small', 'every', 'found',
    'still', 'between', 'mane', 'should', 'home', 'big', 'give', 'air', 'line', 'set', 'own', 'under',
    'read', 'last', 'never', 'am', 'us', 'left', 'end', 'along', 'while', 'might', 'next', 'sound',
    'below', 'saw', 'something', 'thought', 'both', 'few', 'those', 'always', 'looked', 'show', 'large',
    'often', 'together', 'asked', 'house', 'don\'t', 'world', 'going', 'want', 'school', 'important',
    'until', 'form', 'food', 'keep', 'children', 'feet', 'land', 'side', 'without', 'boy', 'once',
    'animal', 'life', 'enough', 'took', 'sometimes', 'four', 'head', 'above', 'kind', 'began', 'almost',
    'live', 'page', 'got', 'earth', 'need', 'far', 'hand', 'high', 'year', 'mother', 'light', 'country',
    'father', 'let', 'night', 'picture', 'being', 'study', 'second', 'soon', 'story', 'since', 'white',
    'ever', 'paper', 'hard', 'near', 'sentence', 'better', 'best', 'across', 'during', 'today', 'however',
    'sure', 'knew', 'it\'s', 'try', 'told', 'young', 'sun', 'thing', 'whole', 'hear', 'example', 'heard',
    'several', 'change', 'answer', 'room', 'sea', 'against', 'top', 'turned', 'learn', 'point', 'city',
    'play', 'toward', 'five', 'himself', 'usually', 'money', 'seen', 'didn\'t', 'car', 'morning', 'i\'m',
    'body', 'upon', 'family', 'later', 'turn', 'move', 'face', 'door', 'cut', 'done', 'group', 'true',
    'leave', 'color', 'red', 'friend', 'pretty', 'eat', 'front', 'feel', 'fact', 'hand', 'week', 'eye',
    'been', 'word', 'great', 'where', 'help', 'through', 'much', 'before', 'line', 'right', 'too', 'any',
    'old', 'same', 'tell', 'boy', 'follow', 'came', 'want', 'show', 'also', 'around', 'farm', 'three',
    'my', 'your', 'his', 'its', 'our', 'their', 'mine', 'yours', 'hers', 'ours', 'theirs',
    'myself', 'yourself', 'himself', 'herself', 'itself', 'ourselves', 'themselves',
    'this', 'that', 'these', 'those', 'very', 'really', 'quite', 'much', 'many', 'more', 'most',
    'some', 'any', 'no', 'not', 'all', 'few', 'less'
]);

/**
 * Preserve case when replacing words
 */
function preserveCase(original, replacement) {
    if (original === original.toUpperCase()) {
        return replacement.toUpperCase();
    } else if (original[0] === original[0].toUpperCase()) {
        return replacement.charAt(0).toUpperCase() + replacement.slice(1).toLowerCase();
    } else {
        return replacement.toLowerCase();
    }
}

/**
 * Simple synonym database for offline use
 */
const SYNONYM_DATABASE = {
    'good': ['great', 'excellent', 'fine', 'nice', 'wonderful'],
    'bad': ['poor', 'terrible', 'awful', 'horrible', 'dreadful'],
    'big': ['large', 'huge', 'enormous', 'massive', 'giant'],
    'small': ['tiny', 'little', 'mini', 'compact', 'petite'],
    'fast': ['quick', 'rapid', 'swift', 'speedy', 'hasty'],
    'slow': ['sluggish', 'gradual', 'leisurely', 'unhurried'],
    'happy': ['joyful', 'cheerful', 'delighted', 'pleased', 'glad'],
    'sad': ['unhappy', 'sorrowful', 'depressed', 'gloomy', 'melancholy'],
    'important': ['significant', 'crucial', 'vital', 'essential', 'key'],
    'easy': ['simple', 'effortless', 'straightforward', 'uncomplicated'],
    'hard': ['difficult', 'challenging', 'tough', 'demanding', 'complex'],
    'new': ['fresh', 'recent', 'modern', 'latest', 'current'],
    'old': ['ancient', 'aged', 'vintage', 'mature', 'elderly'],
    'beautiful': ['gorgeous', 'stunning', 'attractive', 'lovely', 'pretty'],
    'ugly': ['hideous', 'unattractive', 'unsightly', 'repulsive'],
    'smart': ['intelligent', 'clever', 'bright', 'brilliant', 'wise'],
    'stupid': ['foolish', 'dumb', 'ignorant', 'silly', 'senseless'],
    'strong': ['powerful', 'mighty', 'robust', 'sturdy', 'tough'],
    'weak': ['feeble', 'frail', 'fragile', 'delicate', 'vulnerable'],
    'rich': ['wealthy', 'affluent', 'prosperous', 'well-off'],
    'poor': ['impoverished', 'needy', 'destitute', 'broke'],
    'hot': ['warm', 'heated', 'burning', 'scorching', 'blazing'],
    'cold': ['chilly', 'freezing', 'icy', 'frigid', 'cool'],
    'loud': ['noisy', 'boisterous', 'deafening', 'thunderous'],
    'quiet': ['silent', 'peaceful', 'hushed', 'muted', 'soft'],
    'clean': ['spotless', 'pristine', 'immaculate', 'tidy', 'neat'],
    'dirty': ['filthy', 'grimy', 'soiled', 'messy', 'unclean'],
    'bright': ['brilliant', 'radiant', 'luminous', 'vivid', 'glowing'],
    'dark': ['dim', 'gloomy', 'shadowy', 'murky', 'obscure'],
    'funny': ['hilarious', 'amusing', 'comical', 'entertaining', 'witty'],
    'serious': ['grave', 'solemn', 'stern', 'earnest', 'formal'],
    'strange': ['weird', 'odd', 'peculiar', 'unusual', 'bizarre'],
    'normal': ['ordinary', 'typical', 'standard', 'regular', 'common'],
    'dangerous': ['risky', 'hazardous', 'perilous', 'unsafe', 'threatening'],
    'safe': ['secure', 'protected', 'harmless', 'risk-free'],
    'expensive': ['costly', 'pricey', 'dear', 'high-priced'],
    'cheap': ['inexpensive', 'affordable', 'budget', 'low-cost'],
    'thick': ['dense', 'heavy', 'solid', 'substantial'],
    'thin': ['slim', 'slender', 'narrow', 'skinny', 'lean'],
    'wide': ['broad', 'extensive', 'spacious', 'vast'],
    'narrow': ['tight', 'confined', 'restricted', 'limited'],
    'long': ['lengthy', 'extended', 'prolonged', 'extensive'],
    'short': ['brief', 'concise', 'compact', 'quick'],
    'high': ['tall', 'elevated', 'lofty', 'towering'],
    'low': ['short', 'shallow', 'deep', 'sunken'],
    'full': ['complete', 'packed', 'loaded', 'stuffed'],
    'empty': ['vacant', 'hollow', 'bare', 'void'],
    'heavy': ['weighty', 'massive', 'bulky', 'dense'],
    'light': ['lightweight', 'airy', 'delicate', 'feathery'],
    'smooth': ['sleek', 'polished', 'even', 'flat'],
    'rough': ['coarse', 'bumpy', 'uneven', 'jagged'],
    'soft': ['gentle', 'tender', 'smooth', 'delicate'],
    'hard': ['solid', 'firm', 'rigid', 'stiff'],
    'wet': ['damp', 'moist', 'soaked', 'drenched'],
    'dry': ['arid', 'parched', 'dehydrated', 'withered']
};

/**
 * Get synonyms for a word (offline version)
 */
function getSynonyms(word) {
    const lowerWord = word.toLowerCase();
    return SYNONYM_DATABASE[lowerWord] || [];
}

/**
 * Simple paraphrasing with synonym replacement
 */
async function simpleParaphrase(text) {
    let newText = text;
    
    // Split text while preserving punctuation and spacing
    const words = newText.split(/(\s+|[^\w\s]+)/);
    const processedWords = [];

    for (const word of words) {
        if (word.trim() === '' || COMMON_WORDS_TO_SKIP.has(word.toLowerCase()) || !/^[a-zA-Z]+$/.test(word.trim())) {
            processedWords.push(word);
            continue;
        }
        
        // ~20% chance to attempt synonym replacement for eligible words
        if (Math.random() < 0.2) {
            const synonyms = getSynonyms(word.trim().toLowerCase());
            if (synonyms.length > 0) {
                const chosenSynonym = synonyms[Math.floor(Math.random() * Math.min(synonyms.length, 3))]; // Pick from top 3
                processedWords.push(preserveCase(word, chosenSynonym));
            } else {
                processedWords.push(word); // No synonyms found
            }
        } else {
            processedWords.push(word);
        }
    }
    
    newText = processedWords.join('');
    
    // Optional sentence shuffling (very conservative)
    if (Math.random() < 0.1) {
        const sentences = newText.split(/(?<=[.!?])\s+/);
        if (sentences.length > 2 && sentences.length < 6) {
            // Only shuffle if we have a reasonable number of sentences
            const shuffled = [...sentences];
            for (let i = shuffled.length - 1; i > 0; i--) {
                const j = Math.floor(Math.random() * (i + 1));
                [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
            }
            newText = shuffled.join(' ');
        }
    }
    
    return newText;
}

/**
 * Controlled grammar mistakes and human-like anomalies
 */
function addControlledMistakes(text, aggressiveness = 0.1) {
    let newText = text;

    // Common word variations (higher chance for more noticeable changes)
    newText = newText.replace(/\bwhich\b/g, (match) => (Math.random() < 0.15 * aggressiveness ? preserveCase(match, 'that') : match));
    newText = newText.replace(/\bthat\b/g, (match) => (Math.random() < 0.10 * aggressiveness ? preserveCase(match, 'which') : match));

    // Contraction variations
    newText = newText.replace(/\bdo not\b/g, (match) => (Math.random() < 0.3 * aggressiveness ? "don't" : match));
    newText = newText.replace(/\bwill not\b/g, (match) => (Math.random() < 0.3 * aggressiveness ? "won't" : match));
    newText = newText.replace(/\bcannot\b/g, (match) => (Math.random() < 0.3 * aggressiveness ? "can't" : match));
    newText = newText.replace(/\bit is\b/g, (match) => (Math.random() < 0.2 * aggressiveness ? "it's" : match));
    newText = newText.replace(/\bthat is\b/g, (match) => (Math.random() < 0.2 * aggressiveness ? "that's" : match));

    // Reverse contractions occasionally
    newText = newText.replace(/\bdon't\b/g, (match) => (Math.random() < 0.1 * aggressiveness ? "do not" : match));
    newText = newText.replace(/\bwon't\b/g, (match) => (Math.random() < 0.1 * aggressiveness ? "will not" : match));
    newText = newText.replace(/\bcan't\b/g, (match) => (Math.random() < 0.1 * aggressiveness ? "cannot" : match));

    // Word choice variations
    newText = newText.replace(/\bbecause\b/g, (match) => (Math.random() < 0.15 * aggressiveness ? preserveCase(match, 'since') : match));
    newText = newText.replace(/\bsince\b/g, (match) => (Math.random() < 0.10 * aggressiveness ? preserveCase(match, 'because') : match));
    newText = newText.replace(/\balthough\b/g, (match) => (Math.random() < 0.15 * aggressiveness ? preserveCase(match, 'though') : match));
    newText = newText.replace(/\bthough\b/g, (match) => (Math.random() < 0.10 * aggressiveness ? preserveCase(match, 'although') : match));

    // Add filler words occasionally
    const fillerWords = ['actually', 'basically', 'literally', 'obviously', 'clearly'];
    if (Math.random() < 0.1 * aggressiveness) {
        const sentences = newText.split(/(?<=[.!?])\s+/);
        const randomIndex = Math.floor(Math.random() * sentences.length);
        const filler = fillerWords[Math.floor(Math.random() * fillerWords.length)];
        sentences[randomIndex] = filler + ', ' + sentences[randomIndex].charAt(0).toLowerCase() + sentences[randomIndex].slice(1);
        newText = sentences.join(' ');
    }

    // Punctuation variations (more aggressive)
    if (Math.random() < 0.2 * aggressiveness) {
        newText = newText.replace(/, and\b/g, ' and');
        newText = newText.replace(/, but\b/g, ' but');
        newText = newText.replace(/, or\b/g, ' or');
    }

    // Add some natural pauses
    if (Math.random() < 0.15 * aggressiveness) {
        newText = newText.replace(/\. ([A-Z])/g, '. However, $1');
    }

    return newText;
}

/**
 * Style optimization with phrase expansion/contraction and voice changes
 */
function changeStyle(text) {
    let newText = text;

    // Phrase expansions and contractions
    const phraseReplacements = {
        'a lot of': ['many', 'numerous', 'plenty of'],
        'many': ['a lot of', 'numerous', 'plenty of'],
        'very good': ['excellent', 'great', 'outstanding'],
        'very bad': ['terrible', 'awful', 'horrible'],
        'very big': ['huge', 'enormous', 'massive'],
        'very small': ['tiny', 'minuscule', 'microscopic'],
        'in order to': ['to', 'so as to'],
        'due to the fact that': ['because', 'since'],
        'at this point in time': ['now', 'currently'],
        'in the event that': ['if', 'should'],
        'for the purpose of': ['to', 'for'],
        'in spite of the fact that': ['although', 'despite'],
        'it is important to note that': ['note that', 'importantly'],
        'it should be mentioned that': ['notably', 'worth mentioning']
    };

    Object.entries(phraseReplacements).forEach(([phrase, alternatives]) => {
        const regex = new RegExp(`\\b${phrase}\\b`, 'gi');
        newText = newText.replace(regex, (match) => {
            if (Math.random() < 0.3) {
                const alternative = alternatives[Math.floor(Math.random() * alternatives.length)];
                return preserveCase(match, alternative);
            }
            return match;
        });
    });

    // Simple sentence length variation by removing parenthetical phrases occasionally
    if (Math.random() < 0.2) {
        newText = newText.replace(/\s*\([^)]*\)\s*/g, ' ');
        newText = newText.replace(/\s*,\s*[^,]*,\s*/g, (match) => {
            return Math.random() < 0.3 ? ' ' : match;
        });
    }

    // Simple active/passive voice toggling (very basic)
    newText = newText.replace(/(\w+)\s+is\s+(\w+ed)\s+by\s+(\w+)/gi, (match, object, verb, subject) => {
        if (Math.random() < 0.3) {
            return `${subject} ${verb.replace(/ed$/, 's')} ${object}`;
        }
        return match;
    });

    return newText;
}

/**
 * Advanced word replacement to reduce AI-like vocabulary
 */
function replaceAIWords(text) {
    const aiWordReplacements = {
        'utilize': 'use',
        'implement': 'put in place',
        'facilitate': 'help',
        'optimize': 'improve',
        'leverage': 'use',
        'enhance': 'improve',
        'comprehensive': 'complete',
        'extensive': 'wide',
        'significant': 'important',
        'substantial': 'large',
        'furthermore': 'also',
        'moreover': 'also',
        'additionally': 'plus',
        'consequently': 'so',
        'therefore': 'so',
        'delve': 'explore',
        'realm': 'area',
        'landscape': 'scene',
        'paradigm': 'model',
        'framework': 'structure',
        'cutting-edge': 'latest',
        'state-of-the-art': 'modern',
        'innovative': 'new',
        'revolutionary': 'groundbreaking',
        'seamlessly': 'smoothly',
        'effortlessly': 'easily',
        'efficiently': 'well',
        'effectively': 'well',
        'benefits': 'helps'
    };

    let result = text;

    Object.entries(aiWordReplacements).forEach(([aiWord, naturalWord]) => {
        // Only replace 70% of occurrences to avoid being too obvious
        const regex = new RegExp(`\\b${aiWord}\\b`, 'gi');
        result = result.replace(regex, (match) => {
            if (Math.random() > 0.15) { // 85% chance to replace
                return match === match.toLowerCase() ? naturalWord :
                       match === match.toUpperCase() ? naturalWord.toUpperCase() :
                       naturalWord.charAt(0).toUpperCase() + naturalWord.slice(1);
            }
            return match;
        });
    });

    return result;
}

export {
    simpleParaphrase,
    addControlledMistakes,
    changeStyle,
    replaceAIWords,
    getSynonyms,
    preserveCase,
    COMMON_WORDS_TO_SKIP,
    SYNONYM_DATABASE
};
