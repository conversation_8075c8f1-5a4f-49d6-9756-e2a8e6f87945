/**
 * Content Analysis System for Netlify
 * Port of localhost contentAnalyzer.js functionality
 * Provides document structure analysis and content type detection
 */

/**
 * Analyzes text to identify formal document elements and structure
 */
function analyzeDocumentStructure(text) {
    if (!text || typeof text !== 'string') {
        console.warn('Invalid input to analyzeDocumentStructure:', typeof text);
        return {
            hasHeadings: false,
            hasSectionNumbers: false,
            hasListItems: false,
            hasFormalStructure: false,
            preserveFormatting: false,
            formalElements: [],
            protectedRanges: []
        };
    }

    const lines = text.split('\n');
    const analysis = {
        hasHeadings: false,
        hasSectionNumbers: false,
        hasListItems: false,
        hasFormalStructure: false,
        preserveFormatting: false,
        formalElements: [],
        protectedRanges: []
    };

    // Detect various formal elements
    lines.forEach((line, index) => {
        const trimmedLine = line.trim();
        
        // Skip empty lines
        if (!trimmedLine) return;

        // Detect headings and section markers
        if (isHeading(trimmedLine)) {
            analysis.hasHeadings = true;
            analysis.formalElements.push({
                type: 'heading',
                line: index,
                content: trimmedLine
            });
        }

        // Detect section numbers (I., II., A., B., 1., 2., etc.)
        if (isSectionNumber(trimmedLine)) {
            analysis.hasSectionNumbers = true;
            analysis.formalElements.push({
                type: 'section',
                line: index,
                content: trimmedLine
            });
        }

        // Detect list items
        if (isListItem(trimmedLine)) {
            analysis.hasListItems = true;
            analysis.formalElements.push({
                type: 'list',
                line: index,
                content: trimmedLine
            });
        }
    });

    // Determine if document has formal structure
    analysis.hasFormalStructure = analysis.hasHeadings || analysis.hasSectionNumbers || analysis.hasListItems;
    analysis.preserveFormatting = analysis.hasFormalStructure || hasSignificantLineBreaks(text);

    return analysis;
}

/**
 * Check if line is a heading
 */
function isHeading(line) {
    // Check for common heading patterns
    const headingPatterns = [
        /^#{1,6}\s+.+/, // Markdown headings
        /^[A-Z][A-Z\s]{2,}$/, // ALL CAPS headings
        /^\d+\.\s+[A-Z]/, // Numbered headings
        /^[IVX]+\.\s+[A-Z]/, // Roman numeral headings
        /^[A-Z]\.\s+[A-Z]/, // Letter headings
        /^(INTRODUCTION|CONCLUSION|SUMMARY|OVERVIEW|BACKGROUND|METHODOLOGY|RESULTS|DISCUSSION|REFERENCES)$/i
    ];
    
    return headingPatterns.some(pattern => pattern.test(line));
}

/**
 * Check if line is a section number
 */
function isSectionNumber(line) {
    const sectionPatterns = [
        /^[IVX]+\.\s*/, // Roman numerals
        /^\d+\.\s*/, // Arabic numerals
        /^[A-Z]\.\s*/, // Letters
        /^\(\d+\)\s*/, // Parenthetical numbers
        /^\([a-z]\)\s*/ // Parenthetical letters
    ];
    
    return sectionPatterns.some(pattern => pattern.test(line));
}

/**
 * Check if line is a list item
 */
function isListItem(line) {
    const listPatterns = [
        /^[-*+]\s+/, // Bullet points
        /^\d+\.\s+/, // Numbered lists
        /^[a-z]\.\s+/, // Lettered lists
        /^•\s+/, // Bullet character
        /^\(\d+\)\s+/, // Parenthetical numbers
        /^\([a-z]\)\s+/ // Parenthetical letters
    ];
    
    return listPatterns.some(pattern => pattern.test(line));
}

/**
 * Check if text has significant line breaks indicating formatting
 */
function hasSignificantLineBreaks(text) {
    const lines = text.split('\n');
    const nonEmptyLines = lines.filter(line => line.trim().length > 0);
    const emptyLines = lines.length - nonEmptyLines.length;
    
    // If more than 10% of lines are empty, consider it formatted
    return emptyLines / lines.length > 0.1;
}

/**
 * Identify sentences that should be protected from aggressive modification
 */
function identifyProtectedSentences(text, documentAnalysis = {}) {
    const sentences = text.split(/(?<=[.!?])\s+/);
    const protectedSentences = [];
    
    sentences.forEach(sentence => {
        const trimmed = sentence.trim();
        if (!trimmed) return;
        
        if (shouldProtectSentence(trimmed)) {
            protectedSentences.push(trimmed);
        }
    });
    
    return protectedSentences;
}

/**
 * Determine if a sentence should be protected from modification
 */
function shouldProtectSentence(sentence) {
    const trimmed = sentence.trim();
    
    // Protect very short sentences
    if (trimmed.length < 20) return true;
    
    // Protect sentences with formal markers
    const formalMarkers = [
        /^\d+\./,  // Numbered items
        /^[A-Z]\./,  // Lettered items
        /^[-*+•]/,  // Bullet points
        /^(Note:|Warning:|Important:|Example:)/i,
        /^(Figure|Table|Chart|Diagram)\s+\d+/i,
        /\b(see|refer to|as shown in)\s+(figure|table|chart|section|page)\s+\d+/i
    ];
    
    if (formalMarkers.some(pattern => pattern.test(trimmed))) return true;
    
    // Protect sentences with technical terms or code-like content
    if (hasTechnicalContent(trimmed)) return true;
    
    // Protect sentences that are mostly uppercase (likely headings)
    const uppercaseRatio = (trimmed.match(/[A-Z]/g) || []).length / trimmed.length;
    if (uppercaseRatio > 0.5) return true;
    
    return false;
}

/**
 * Check if sentence contains technical content that should be preserved
 */
function hasTechnicalContent(sentence) {
    const technicalPatterns = [
        /\b[A-Z]{2,}\b/,                // Acronyms
        /\b\w+\.\w+\b/,                 // Domain names or file extensions
        /\b\d+\.\d+\b/,                 // Version numbers
        /[{}[\]()]/,                    // Brackets/braces (code-like)
        /\b(API|URL|HTTP|JSON|XML|CSS|HTML|JS)\b/i,
        /\b(function|class|method|property|variable)\b/i
    ];
    
    return technicalPatterns.some(pattern => pattern.test(sentence));
}

/**
 * Calculate appropriate hesitation frequency based on document analysis
 */
function calculateHesitationFrequency(documentAnalysis) {
    let baseFrequency = 0.05; // 5% base frequency
    
    // Reduce frequency for formal documents
    if (documentAnalysis.hasFormalStructure) {
        baseFrequency *= 0.5;
    }
    
    // Further reduce for documents with many formal elements
    if (documentAnalysis.formalElements && documentAnalysis.formalElements.length > 5) {
        baseFrequency *= 0.3;
    }
    
    return Math.max(0.01, baseFrequency); // Minimum 1% frequency
}

/**
 * Analyze content type for specialized processing
 */
function analyzeContentType(text) {
    const analysis = {
        type: 'general',
        characteristics: [],
        processingStrategy: 'standard'
    };

    // Detect formal documents
    if (text.match(/\b(executive summary|introduction|methodology|conclusion|references|bibliography)\b/gi)) {
        analysis.type = 'formal_document';
        analysis.characteristics.push('structured_sections');
        analysis.processingStrategy = 'conservative_formal';
    }

    // Detect technical content
    if (text.match(/\b(API|algorithm|database|framework|implementation|configuration|deployment)\b/gi)) {
        analysis.type = 'technical';
        analysis.characteristics.push('technical_terminology');
        analysis.processingStrategy = 'preserve_technical_terms';
    }

    // Detect academic writing
    if (text.match(/\b(research|study|analysis|findings|hypothesis|methodology|literature review)\b/gi)) {
        analysis.type = 'academic';
        analysis.characteristics.push('research_oriented');
        analysis.processingStrategy = 'academic_style_preservation';
    }

    // Detect business content
    if (text.match(/\b(strategy|revenue|market|customer|business|organization|management|operations)\b/gi)) {
        analysis.type = 'business';
        analysis.characteristics.push('business_terminology');
        analysis.processingStrategy = 'professional_tone_maintenance';
    }

    return analysis;
}

export {
    analyzeDocumentStructure,
    identifyProtectedSentences,
    calculateHesitationFrequency,
    analyzeContentType,
    isHeading,
    isSectionNumber,
    isListItem,
    hasSignificantLineBreaks,
    shouldProtectSentence,
    hasTechnicalContent
};
