/**
 * Balanced Humanizer for Netlify
 * Complete port of localhost balancedHumanizer.js functionality
 * Integrates advanced humanization techniques with style profile support
 */

// Note: Avoiding circular import with advanced-humanizer.js
// Advanced humanization will be imported dynamically when needed
import {
    simpleParaphrase,
    addControlledMistakes,
    changeStyle,
    replaceAIWords
} from './text-modifiers-advanced.js';

/**
 * Main balanced humanization function - ENHANCED VERSION
 * Uses advanced techniques to achieve target ≤10% AI detection
 */
async function balancedHumanization(input, styleProfile = null, styleStrength = 0, options = {}) {
    console.log('Balanced humanization called with:', { 
        inputType: typeof input, 
        hasText: input && input.text ? 'yes' : 'no',
        styleProfile: styleProfile ? styleProfile.name : 'none',
        styleStrength 
    });

    // Handle different input formats
    let text;
    if (typeof input === 'string') {
        text = input;
    } else if (input && typeof input.text === 'string') {
        text = input.text;
    } else if (input && typeof input === 'object') {
        // Try to find text in various possible properties
        text = input.modifiedText || input.humanizedText || input.content || '';
    } else {
        console.error('balancedHumanization: Invalid input format', { input, type: typeof input });
        return input || '';
    }

    // If style profile is provided, use integrated style application
    if (styleProfile && styleStrength > 0) {
        return await applyWritingStyleIntegrated(text, styleProfile, styleStrength, options);
    }

    const {
        useAdvanced = true, // Use advanced humanization by default
        aggressiveness = 0.6, // Moderate aggressiveness for balance
        preserveLength = false,
        maintainTone = true
    } = options;

    let result = text;

    // Additional safety check before processing
    if (!result || typeof result !== 'string') {
        console.error('balancedHumanization: result is not a valid string before processing', { result, type: typeof result });
        return input || '';
    }

    try {
        if (useAdvanced) {
            console.log('🚀 Using advanced humanization for balanced approach...');

            try {
                // Dynamic import to avoid circular dependency
                const { advancedHumanization } = await import('./advanced-humanizer.js');

                // Use the advanced humanization engine
                const advancedResult = advancedHumanization(result, {
                    aggressiveness,
                    maintainTone,
                    targetDetection: 10, // Target ≤10% for balanced approach
                    useModelBased: false, // Pattern-based only for Netlify
                    fallbackToPatterns: true
                });

                if (advancedResult.success) {
                    result = advancedResult.text;
                    console.log(`Advanced humanization completed. Length: ${result.length}`);
                } else {
                    console.warn('Advanced humanization failed, falling back to basic techniques');
                    result = await applyBasicHumanizationTechniques(result, aggressiveness);
                }
            } catch (error) {
                console.error('Failed to load advanced humanization:', error);
                console.log('📝 Falling back to basic humanization techniques...');
                result = await applyBasicHumanizationTechniques(result, aggressiveness);
            }
        } else {
            console.log('📝 Using basic humanization techniques...');
            result = await applyBasicHumanizationTechniques(result, aggressiveness);
        }

        // Apply additional AI word replacement
        result = replaceAIWords(result);

        // Final quality check
        if (preserveLength) {
            const lengthDifference = Math.abs(result.length - text.length) / text.length;
            if (lengthDifference > 0.2) { // If length changed by more than 20%
                console.warn('Length preservation failed, reverting to less aggressive processing');
                result = await applyBasicHumanizationTechniques(text, aggressiveness * 0.5);
            }
        }

        console.log(`Balanced humanization completed. Original: ${text.length} chars, Result: ${result.length} chars`);
        return result;

    } catch (error) {
        console.error('Error in balancedHumanization:', error);
        return text; // Return original text on error
    }
}

/**
 * Apply basic humanization techniques as fallback
 */
async function applyBasicHumanizationTechniques(text, aggressiveness) {
    let result = text;

    try {
        // Step 1: Simple paraphrasing with synonym replacement
        result = await simpleParaphrase(result);
        
        // Step 2: Style changes
        result = changeStyle(result);
        
        // Step 3: Controlled mistakes for human-like imperfections
        result = addControlledMistakes(result, aggressiveness * 0.3);
        
        // Step 4: AI word replacement
        result = replaceAIWords(result);

        return result;
    } catch (error) {
        console.error('Error in basic humanization techniques:', error);
        return text;
    }
}

/**
 * Apply writing style with integrated humanization
 */
async function applyWritingStyleIntegrated(text, styleProfile, styleStrength, options = {}) {
    const { aggressiveness = 0.6 } = options;
    
    let result = text;
    const intensity = styleStrength / 100;

    try {
        // Apply base humanization first
        result = await applyBasicHumanizationTechniques(result, aggressiveness);

        // Apply style-specific modifications
        if (styleProfile.name === 'casual') {
            result = applyCasualStyle(result, intensity);
        } else if (styleProfile.name === 'professional') {
            result = applyProfessionalStyle(result, intensity);
        } else if (styleProfile.name === 'academic') {
            result = applyAcademicStyle(result, intensity);
        } else if (styleProfile.name === 'creative') {
            result = applyCreativeStyle(result, intensity);
        } else if (styleProfile.name === 'technical') {
            result = applyTechnicalStyle(result, intensity);
        }

        return result;
    } catch (error) {
        console.error('Error in style application:', error);
        return text;
    }
}

/**
 * Apply casual writing style
 */
function applyCasualStyle(text, intensity) {
    let result = text;

    // Add more contractions
    const contractions = [
        ['do not', "don't"],
        ['will not', "won't"],
        ['cannot', "can't"],
        ['it is', "it's"],
        ['that is', "that's"],
        ['we are', "we're"],
        ['you are', "you're"],
        ['they are', "they're"]
    ];

    contractions.forEach(([full, contracted]) => {
        const regex = new RegExp(`\\b${full}\\b`, 'gi');
        result = result.replace(regex, (match) => {
            return Math.random() < intensity * 0.8 ? contracted : match;
        });
    });

    // Add casual phrases
    const casualPhrases = [
        'you know', 'I mean', 'like', 'pretty much', 'basically', 'anyway'
    ];

    if (Math.random() < intensity * 0.3) {
        const sentences = result.split(/(?<=[.!?])\s+/);
        const randomIndex = Math.floor(Math.random() * sentences.length);
        const phrase = casualPhrases[Math.floor(Math.random() * casualPhrases.length)];
        sentences[randomIndex] = phrase + ', ' + sentences[randomIndex].charAt(0).toLowerCase() + sentences[randomIndex].slice(1);
        result = sentences.join(' ');
    }

    // More controlled mistakes for casual tone
    result = addControlledMistakes(result, intensity * 0.2);

    return result;
}

/**
 * Apply professional writing style
 */
function applyProfessionalStyle(text, intensity) {
    let result = text;

    // Reduce contractions for more formal tone
    const expansions = [
        ["don't", 'do not'],
        ["won't", 'will not'],
        ["can't", 'cannot'],
        ["it's", 'it is'],
        ["that's", 'that is']
    ];

    expansions.forEach(([contracted, full]) => {
        const regex = new RegExp(`\\b${contracted}\\b`, 'gi');
        result = result.replace(regex, (match) => {
            return Math.random() < intensity * 0.6 ? full : match;
        });
    });

    // Add professional transitions
    const professionalTransitions = [
        'Furthermore', 'Moreover', 'Additionally', 'Consequently', 'Therefore'
    ];

    if (Math.random() < intensity * 0.2) {
        const sentences = result.split(/(?<=[.!?])\s+/);
        if (sentences.length > 1) {
            const randomIndex = Math.floor(Math.random() * (sentences.length - 1)) + 1;
            const transition = professionalTransitions[Math.floor(Math.random() * professionalTransitions.length)];
            sentences[randomIndex] = transition + ', ' + sentences[randomIndex].charAt(0).toLowerCase() + sentences[randomIndex].slice(1);
            result = sentences.join(' ');
        }
    }

    // Minimal controlled mistakes for professional tone
    result = addControlledMistakes(result, intensity * 0.05);

    return result;
}

/**
 * Apply academic writing style
 */
function applyAcademicStyle(text, intensity) {
    let result = text;

    // Add academic phrases
    const academicPhrases = [
        'It should be noted that',
        'Research indicates that',
        'Studies have shown that',
        'Evidence suggests that',
        'It is important to consider that'
    ];

    if (Math.random() < intensity * 0.15) {
        const sentences = result.split(/(?<=[.!?])\s+/);
        const randomIndex = Math.floor(Math.random() * sentences.length);
        const phrase = academicPhrases[Math.floor(Math.random() * academicPhrases.length)];
        sentences[randomIndex] = phrase + ' ' + sentences[randomIndex].charAt(0).toLowerCase() + sentences[randomIndex].slice(1);
        result = sentences.join(' ');
    }

    // Very minimal controlled mistakes for academic tone
    result = addControlledMistakes(result, intensity * 0.02);

    return result;
}

/**
 * Apply creative writing style
 */
function applyCreativeStyle(text, intensity) {
    let result = text;

    // Add creative elements and varied sentence structures
    result = changeStyle(result); // Apply more aggressive style changes
    
    // Add more controlled mistakes for creative flair
    result = addControlledMistakes(result, intensity * 0.15);

    return result;
}

/**
 * Apply technical writing style
 */
function applyTechnicalStyle(text, intensity) {
    let result = text;

    // Keep technical terms intact, minimal changes
    result = addControlledMistakes(result, intensity * 0.03);

    return result;
}

export {
    balancedHumanization,
    applyBasicHumanizationTechniques,
    applyWritingStyleIntegrated,
    applyCasualStyle,
    applyProfessionalStyle,
    applyAcademicStyle,
    applyCreativeStyle,
    applyTechnicalStyle
};
