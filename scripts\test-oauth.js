#!/usr/bin/env node

/**
 * OAuth Configuration Test Script
 * Tests Google OAuth setup and environment configuration
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Get __dirname equivalent in ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('🔐 GOOGLE OAUTH CONFIGURATION TEST\n');

// Test 1: Check if .env.local exists
console.log('📋 TEST 1: Environment File Check');
const envPath = path.join(process.cwd(), '.env.local');
if (fs.existsSync(envPath)) {
    console.log('✅ .env.local file exists');
} else {
    console.log('❌ .env.local file missing');
    console.log('   Create .env.local file in project root');
    process.exit(1);
}

// Test 2: Load environment variables
console.log('\n📋 TEST 2: Environment Variables');
require('dotenv').config({ path: '.env.local' });

const requiredVars = [
    'NEXTAUTH_SECRET',
    'NEXTAUTH_URL', 
    'GOOGLE_CLIENT_ID',
    'GOOGLE_CLIENT_SECRET',
    'DATABASE_URL'
];

let missingVars = [];
let placeholderVars = [];

requiredVars.forEach(varName => {
    const value = process.env[varName];
    if (!value) {
        missingVars.push(varName);
        console.log(`❌ ${varName}: Missing`);
    } else if (value.includes('your_') || value.includes('replace') || value.includes('here')) {
        placeholderVars.push(varName);
        console.log(`⚠️  ${varName}: Contains placeholder text`);
    } else {
        console.log(`✅ ${varName}: Configured`);
    }
});

// Test 3: Validate NEXTAUTH_SECRET
console.log('\n📋 TEST 3: NEXTAUTH_SECRET Validation');
const nextAuthSecret = process.env.NEXTAUTH_SECRET;
if (nextAuthSecret && nextAuthSecret.length >= 32) {
    console.log('✅ NEXTAUTH_SECRET: Proper length (32+ characters)');
} else {
    console.log('❌ NEXTAUTH_SECRET: Too short (needs 32+ characters)');
    console.log('   Generate with: node -e "console.log(require(\'crypto\').randomBytes(32).toString(\'base64\'))"');
}

// Test 4: Validate Google OAuth format
console.log('\n📋 TEST 4: Google OAuth Format Validation');
const clientId = process.env.GOOGLE_CLIENT_ID;
const clientSecret = process.env.GOOGLE_CLIENT_SECRET;

if (clientId && clientId.endsWith('.apps.googleusercontent.com')) {
    console.log('✅ GOOGLE_CLIENT_ID: Correct format');
} else {
    console.log('❌ GOOGLE_CLIENT_ID: Invalid format (should end with .apps.googleusercontent.com)');
}

if (clientSecret && clientSecret.startsWith('GOCSPX-')) {
    console.log('✅ GOOGLE_CLIENT_SECRET: Correct format');
} else {
    console.log('❌ GOOGLE_CLIENT_SECRET: Invalid format (should start with GOCSPX-)');
}

// Test 5: Database file check
console.log('\n📋 TEST 5: Database Check');
const dbPath = path.join(process.cwd(), 'prisma', 'dev.db');
if (fs.existsSync(dbPath)) {
    console.log('✅ Database file exists');
} else {
    console.log('⚠️  Database file missing (will be created on first run)');
    console.log('   Run: npx prisma db push');
}

// Test 6: NextAuth configuration check
console.log('\n📋 TEST 6: NextAuth Configuration');
const nextAuthPath = path.join(process.cwd(), 'src', 'pages', 'api', 'auth', '[...nextauth].js');
if (fs.existsSync(nextAuthPath)) {
    console.log('✅ NextAuth configuration file exists');
    
    // Check if Google provider is configured
    const nextAuthContent = fs.readFileSync(nextAuthPath, 'utf8');
    if (nextAuthContent.includes('GoogleProvider')) {
        console.log('✅ Google provider configured in NextAuth');
    } else {
        console.log('❌ Google provider missing in NextAuth configuration');
    }
} else {
    console.log('❌ NextAuth configuration file missing');
}

// Summary
console.log('\n📊 SUMMARY');
if (missingVars.length > 0) {
    console.log(`❌ Missing variables: ${missingVars.join(', ')}`);
}
if (placeholderVars.length > 0) {
    console.log(`⚠️  Placeholder variables (need real values): ${placeholderVars.join(', ')}`);
}

if (missingVars.length === 0 && placeholderVars.length === 0) {
    console.log('✅ All environment variables configured correctly!');
    console.log('\n🚀 NEXT STEPS:');
    console.log('1. Start development server: npm run dev');
    console.log('2. Open http://localhost:3000');
    console.log('3. Click "Sign in with Google"');
    console.log('4. Test OAuth flow');
} else {
    console.log('\n🔧 ACTION REQUIRED:');
    console.log('1. Follow GOOGLE_OAUTH_FIX.md guide');
    console.log('2. Set up Google Cloud Console OAuth credentials');
    console.log('3. Update .env.local with real values');
    console.log('4. Run this test again: node scripts/test-oauth.js');
}

console.log('\n📖 For detailed setup instructions, see: GOOGLE_OAUTH_FIX.md');
