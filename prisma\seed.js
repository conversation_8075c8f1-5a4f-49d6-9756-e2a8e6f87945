import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Starting database seeding...');

  // Create sample users for development
  const sampleUsers = [
    {
      id: 'sample-user-1',
      name: '<PERSON>',
      email: '<EMAIL>',
      subscriptionTier: 'free',
      usageCredits: 10,
    },
    {
      id: 'sample-user-2',
      name: '<PERSON>',
      email: '<EMAIL>',
      subscriptionTier: 'premium_monthly',
      usageCredits: 1000,
    },
  ];

  for (const userData of sampleUsers) {
    const user = await prisma.user.upsert({
      where: { email: userData.email },
      update: {},
      create: userData,
    });
    console.log(`✅ Created/Updated user: ${user.email}`);
  }

  // Create sample subscriptions for premium user
  const premiumUser = await prisma.user.findUnique({
    where: { email: '<EMAIL>' },
  });

  if (premiumUser) {
    await prisma.subscription.upsert({
      where: { stripeSubscriptionId: 'sub_sample_premium' },
      update: {},
      create: {
        userId: premiumUser.id,
        stripeSubscriptionId: 'sub_sample_premium',
        stripeCustomerId: 'cus_sample_customer',
        stripePriceId: 'price_sample_premium_monthly',
        status: 'active',
        stripeCurrentPeriodEnd: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
      },
    });
    console.log('✅ Created sample subscription for premium user');
  }

  console.log('🎉 Database seeding completed!');
}

main()
  .catch((e) => {
    console.error('❌ Error during seeding:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
