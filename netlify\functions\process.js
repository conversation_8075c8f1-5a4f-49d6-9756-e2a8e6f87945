// netlify/functions/process.js
// Enhanced text processing function for Netlify with complete localhost functionality
// Uses ES modules to match the rest of the codebase

// Import the self-contained NLTK humanizer (ES modules)
import {
    humanizeWithNLTKApproach,
    applyBasicHumanization
} from './nltk-humanizer.js';

// Import text transformations (ES modules)
import { balancedHumanization, qualityCheck } from './text-transformations.js';

// Import advanced humanization components
import { advancedHumanization } from './advanced-humanizer.js';
import { balancedHumanization as advancedBalancedHumanization } from './balanced-humanizer.js';
import { commercialEngine } from './commercial-humanizer.js';
import {
    analyzeDocumentStructure,
    identifyProtectedSentences,
    calculateHesitationFrequency,
    analyzeContentType
} from './content-analyzer.js';

export const handler = async (event, context) => {
    // Set CORS headers
    const headers = {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Content-Type': 'application/json',
    };

    // Handle preflight requests
    if (event.httpMethod === 'OPTIONS') {
        return {
            statusCode: 200,
            headers,
            body: '',
        };
    }

    // Only allow POST method
    if (event.httpMethod !== 'POST') {
        return {
            statusCode: 405,
            headers,
            body: JSON.stringify({ message: `Method ${event.httpMethod} Not Allowed` }),
        };
    }

    try {
        const {
            text,
            styleProfile,
            styleStrength,
            method = 'auto', // 'auto', 'advanced', 'commercial', 'nltk', 'balanced'
            aggressiveness = 0.7,
            targetDetection = 10,
            commercialGrade = false
        } = JSON.parse(event.body);

        if (!text || typeof text !== 'string' || !text.trim()) {
            return {
                statusCode: 400,
                headers,
                body: JSON.stringify({ message: 'Input text is required and must be a non-empty string.' }),
            };
        }

        // Validate style parameters if provided
        if (styleStrength !== undefined && (typeof styleStrength !== 'number' || styleStrength < 0 || styleStrength > 100)) {
            return {
                statusCode: 400,
                headers,
                body: JSON.stringify({ message: 'Style strength must be a number between 0 and 100.' }),
            };
        }

        // Validate method parameter
        const validMethods = ['auto', 'advanced', 'commercial', 'nltk', 'balanced'];
        if (!validMethods.includes(method)) {
            return {
                statusCode: 400,
                headers,
                body: JSON.stringify({ message: `Invalid method. Must be one of: ${validMethods.join(', ')}` }),
            };
        }

        let modifiedText = text; // Start with the original text
        let processingMethod = method;
        let humanizationResult = null;

        const startTime = Date.now();

        // Analyze content for informed processing
        console.log("🔍 Analyzing content structure and type...");
        const documentAnalysis = analyzeDocumentStructure(text);
        const contentTypeAnalysis = analyzeContentType(text);
        const protectedSentences = identifyProtectedSentences(text, documentAnalysis);
        const hesitationFrequency = calculateHesitationFrequency(documentAnalysis);

        console.log(`Content analysis complete:`, {
            type: contentTypeAnalysis.type,
            strategy: contentTypeAnalysis.processingStrategy,
            formalElements: documentAnalysis.formalElements.length,
            protectedSentences: protectedSentences.length,
            hesitationFrequency
        });

        // Determine processing method based on request and content analysis
        if (method === 'auto') {
            // Auto-select method based on target detection and content type
            if (commercialGrade || targetDetection <= 5) {
                processingMethod = 'commercial';
            } else if (targetDetection <= 10 && contentTypeAnalysis.type !== 'technical') {
                processingMethod = 'advanced';
            } else if (styleProfile && styleStrength > 0) {
                processingMethod = 'balanced';
            } else {
                processingMethod = 'nltk';
            }
        }

        console.log(`🚀 Starting ${processingMethod} humanization method...`);

        // Calculate target aggressiveness based on style strength and method
        const baseAggressiveness = aggressiveness;
        const adjustedAggressiveness = styleStrength ?
            Math.min(0.95, baseAggressiveness + (styleStrength / 100) * 0.3) :
            baseAggressiveness;

        // --- Main Processing Pipeline ---
        try {

            switch (processingMethod) {
                case 'commercial':
                    console.log("🏭 Using commercial-grade humanization...");
                    humanizationResult = await commercialEngine.humanizeCommercial(modifiedText, {
                        aggressiveness: adjustedAggressiveness,
                        maintainTone: true,
                        targetDetection: Math.min(targetDetection, 5),
                        personality: 'casual_expert'
                    });

                    if (humanizationResult.success) {
                        modifiedText = humanizationResult.text;
                        console.log(`Commercial humanization completed: ${humanizationResult.qualityMetrics.grade}`);
                    } else {
                        throw new Error(`Commercial humanization failed: ${humanizationResult.error}`);
                    }
                    break;

                case 'advanced':
                    console.log("🔧 Using advanced pattern-based humanization...");
                    humanizationResult = advancedHumanization(modifiedText, {
                        aggressiveness: adjustedAggressiveness,
                        maintainTone: true,
                        targetDetection,
                        useModelBased: false, // Pattern-based only for Netlify
                        fallbackToPatterns: true
                    });

                    if (humanizationResult.success) {
                        modifiedText = humanizationResult.text;
                        console.log(`Advanced humanization completed in ${humanizationResult.processingTime}ms`);
                    } else {
                        throw new Error(`Advanced humanization failed: ${humanizationResult.error}`);
                    }
                    break;

                case 'balanced':
                    console.log("⚖️ Using balanced humanization with style integration...");
                    modifiedText = await advancedBalancedHumanization(modifiedText, styleProfile, styleStrength, {
                        aggressiveness: adjustedAggressiveness,
                        useAdvanced: true,
                        maintainTone: true
                    });
                    console.log("Balanced humanization with style integration completed");
                    break;

                case 'nltk':
                default:
                    console.log("🔬 Using NLTK-inspired humanization...");
                    humanizationResult = await humanizeWithNLTKApproach(modifiedText, {
                        aggressiveness: adjustedAggressiveness,
                        maintainTone: true,
                        targetDetection,
                        useAdvancedSynonyms: true,
                        user: null // Could be extracted from headers/auth in future
                    });

                    if (humanizationResult.success) {
                        modifiedText = humanizationResult.text;
                        console.log(`NLTK humanization completed in ${humanizationResult.processingTime}ms`);
                        console.log(`User tier: ${humanizationResult.userTier || 'free'}`);
                    } else {
                        throw new Error(`NLTK humanization failed: ${humanizationResult.error}`);
                    }
                    break;
            }

        } catch (error) {
            console.error(`${processingMethod} humanization error:`, error.message);
            console.log("🔄 Falling back to basic humanization...");

            // Fallback to basic humanization
            modifiedText = applyBasicHumanization(modifiedText, {
                aggressiveness: adjustedAggressiveness * 0.8
            });

            // Update processing method to indicate fallback
            processingMethod = `${processingMethod}-fallback`;
            console.log("Basic humanization fallback completed.");
        }

        // --- Step 2: Apply additional balanced humanization if not already applied ---
        if (processingMethod !== 'balanced' && (styleProfile && styleStrength > 0)) {
            console.log("🎨 Applying additional style integration...");
            modifiedText = balancedHumanization(modifiedText, styleProfile, styleStrength, {
                aggressiveness: adjustedAggressiveness * 0.8,
                maintainTone: true
            });
        } else if (processingMethod === 'nltk') {
            // Apply standard balanced humanization for NLTK method
            console.log("⚖️ Applying balanced humanization refinement...");
            modifiedText = balancedHumanization(modifiedText, null, 0, {
                aggressiveness: adjustedAggressiveness * 0.6,
                maintainTone: true
            });
        }

        // --- Step 3: Quality check ---
        console.log("✅ Performing quality check...");
        const qualityResult = qualityCheck(modifiedText);

        if (qualityResult.hasIssues) {
            console.log("⚠️ Quality issues detected:", qualityResult.issues);
            // Apply additional basic humanization if needed
            modifiedText = applyBasicHumanization(modifiedText, {
                aggressiveness: adjustedAggressiveness * 0.5,
                addMistakes: true,
                changeStyleFlag: false
            });
        } else {
            console.log("✅ Quality check passed");
        }

        // --- Step 4: Final AI Detection Check (Enhanced Mock for Netlify) ---
        console.log("🎯 Performing AI detection estimation...");

        // Enhanced mock detection based on processing method and transformations
        let estimatedScore;
        switch (processingMethod) {
            case 'commercial':
                estimatedScore = Math.random() * 3 + 2; // 2-5% for commercial
                break;
            case 'advanced':
                estimatedScore = Math.random() * 5 + 5; // 5-10% for advanced
                break;
            case 'balanced':
                estimatedScore = Math.random() * 8 + 7; // 7-15% for balanced
                break;
            case 'nltk':
            default:
                estimatedScore = Math.random() * 10 + 10; // 10-20% for NLTK
                break;
        }

        // Adjust score based on transformation rate
        const transformationRate = Math.abs((modifiedText.length - text.length) / text.length);
        if (transformationRate > 0.2) {
            estimatedScore *= 0.8; // Reduce score for significant transformations
        }

        const detectionResult = {
            error: false,
            status: "PROCESSED",
            score: Math.max(1, Math.min(25, estimatedScore)), // Clamp between 1-25%
            message: `Text processed successfully with ${processingMethod} humanization`,
            method: processingMethod,
            confidence: processingMethod === 'commercial' ? 'high' :
                       processingMethod === 'advanced' ? 'medium-high' : 'medium'
        };

        const totalProcessingTime = Date.now() - startTime;

        // Prepare comprehensive response metadata
        const responseMetadata = {
            success: true,
            method: processingMethod,
            processingTime: totalProcessingTime,
            originalLength: text.length,
            newLength: modifiedText.length,
            transformationRate: ((Math.abs(text.length - modifiedText.length)) / text.length * 100).toFixed(2),
            contentAnalysis: {
                type: contentTypeAnalysis.type,
                strategy: contentTypeAnalysis.processingStrategy,
                formalElements: documentAnalysis.formalElements.length,
                protectedSentences: protectedSentences.length,
                hesitationFrequency
            },
            qualityMetrics: {
                hasIssues: qualityResult.hasIssues,
                issues: qualityResult.issues || [],
                estimatedDetection: detectionResult.score.toFixed(1) + '%',
                confidence: detectionResult.confidence
            },
            userTier: humanizationResult?.userTier || 'free',
            timestamp: new Date().toISOString()
        };

        // Add method-specific metadata
        if (humanizationResult) {
            if (processingMethod === 'commercial' && humanizationResult.qualityMetrics) {
                responseMetadata.commercialMetrics = humanizationResult.qualityMetrics;
                responseMetadata.guarantees = humanizationResult.guarantees;
            } else if (processingMethod === 'advanced' && humanizationResult.aiAnalysis) {
                responseMetadata.aiAnalysis = humanizationResult.aiAnalysis;
                responseMetadata.adjustedAggressiveness = humanizationResult.adjustedAggressiveness;
            }
        }

        console.log(`🎉 Processing completed successfully in ${totalProcessingTime}ms`);
        console.log(`Method: ${processingMethod}, Detection: ${detectionResult.score.toFixed(1)}%, Transform: ${responseMetadata.transformationRate}%`);

        return {
            statusCode: 200,
            headers,
            body: JSON.stringify({
                modifiedText,
                detectionResult,
                ...responseMetadata
            }),
        };

    } catch (error) {
        console.error("❌ Error in /api/process:", error);
        const errorMessage = error.message || 'Error processing text.';
        const processingTime = startTime ? Date.now() - startTime : 0;

        // Try to provide fallback processing even on error
        let fallbackText = text;
        try {
            console.log("🔄 Attempting emergency fallback processing...");
            fallbackText = applyBasicHumanization(text, {
                aggressiveness: 0.5
            });
            console.log("✅ Emergency fallback completed");
        } catch (fallbackError) {
            console.error("❌ Emergency fallback also failed:", fallbackError);
        }

        return {
            statusCode: 500,
            headers,
            body: JSON.stringify({
                success: false,
                message: errorMessage,
                error: error.toString(),
                modifiedText: fallbackText, // Provide fallback text
                method: 'emergency-fallback',
                processingTime,
                originalLength: text.length,
                newLength: fallbackText.length,
                detectionResult: {
                    error: true,
                    status: "Server Error",
                    message: "Failed to process text due to an internal server error. Emergency fallback applied.",
                    score: null,
                    method: 'emergency-fallback'
                },
                timestamp: new Date().toISOString()
            }),
        };
    }
};
